default:
  image: docker:24.0.5
  services:
    - docker:24.0.5-dind
  before_script:
    - docker info

cache:
  key: '$CI_BUILD_NAME'
  untracked: true

stages:
  - build_deploy

variables:
  DOCKER_HOST: tcp://docker:2375
  DOCKER_TLS_CERTDIR: ''
  AWS_REGION: $AWS_REGION
  AWS_ACCESS_KEY_ID: $AWS_ACCESS_KEY_ID
  AWS_SECRET_ACCESS_KEY: $AWS_SECRET_ACCESS_KEY
  ECR_REPOSITORY: $ECR_REPOSITORY
  IMAGE_TAG: $ECR_REPOSITORY:latest

build_deploy:
  stage: build_deploy
  only:
    - main
  before_script:
    - docker info
    - apk add --update --no-cache curl py-pip
    - apk add --update --no-cache curl jq py3-configobj py3-pip py3-setuptools python3 python3-dev
    - pip install awscli
    - export AWS_REGION=$AWS_REGION
    - export AWS_ACCESS_KEY_ID=$AWS_ACCESS_KEY_ID
    - export AWS_SECRET_ACCESS_KEY=$AWS_SECRET_ACCESS_KEY
    - export ECR_REPOSITORY=$ECR_REPOSITORY
  script:
    - $(aws ecr get-login --no-include-email --region $AWS_REGION)
    - docker build -f Dockerfile -t $ECR_REPOSITORY .
    - docker push $ECR_REPOSITORY
    - aws ecs update-service --cluster DevelopCluster --service api --force-new-deployment --region $AWS_REGION
