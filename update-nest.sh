#!/bin/bash

#export $(grep -v '^#' .env | xargs -d '\n')
#npm run start:dev

# First, update the NestJS CLI to the latest version by running the following command:
npm install -g @nestjs/cli

# Verify the installed version
nest --version

# Update your dependencies, install the npm-check-updates package globally
npm i -g npm-check-updates

# Use ncu -u to update your package.json file with the latest dependencies.
ncu -u -f /^@nestjs/

# Delete the package-lock.json file and the node_modules folder
rm -rf node_modules package-lock.json

# Finally, install the updated dependencies:
npm install
