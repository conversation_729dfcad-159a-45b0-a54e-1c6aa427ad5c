# Develop Platform Api

## Development Environment (Docker)

To run this project it is necessary to define some variables, for that make a copy of the file
'.env.example' and renamed it to '.env' and update the values according to your environment.

### Docker

To run the project execute: `docker-compose -f docker-compose.dev.yaml up --build`
To clear volumes execute `docker-compose -f docker-compose.dev.yaml down --volume`

### Without Docker

## Migrations

### Useful commands:

Generate automatic migrations: `npm run migration:generate --name=name`
Run the migration: `npm run migration:run`
Revert applyed migration: `npm run migration:revert`
"typeorm": "typeorm-ts-node-commonjs",
