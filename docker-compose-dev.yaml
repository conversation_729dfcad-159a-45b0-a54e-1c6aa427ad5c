services:
  api:
    image: develop/api
    container_name: develop-api
    build:
      context: .
      dockerfile: .docker/dev/Dockerfile
    volumes:
      - .:/usr/src/app
    env_file:
      - ./.env
    ports:
      - 3000:3000
    command: .docker/dev/start.sh
    networks:
      - develop_network

  db:
    image: postgres:latest
    container_name: develop-db
    ports:
      - '${POSTGRES_PORT:-5432}:5432'
    volumes:
      - ./.docker/db/postgres_data:/var/lib/postgresql/data
    environment:
      POSTGRES_USER: '${POSTGRES_USER:-postgres}'
      POSTGRES_PASSWORD: '${POSTGRES_PASSWORD:-postgres}'
      POSTGRES_DB: '${POSTGRES_DB:-develop_db}'
    networks:
      - develop_network

  rabbitmq:
    image: rabbitmq:management
    container_name: develop-rabbitmq
    ports:
      - '${RABBITMQ_PORT:-5672}:5672'
      - '${RABBITMQ_MANAGEMENT_PORT:-15672}:15672'
    volumes:
      - rabbitmq_data:/var/lib/rabbitmq
    environment:
      RABBITMQ_DEFAULT_USER: '${RABBITMQ_USER:-admin}'
      RABBITMQ_DEFAULT_PASS: '${RABBITMQ_PASSWORD:-admin}'
    networks:
      - develop_network

networks:
  develop_network:
    name: develop_network
    driver: bridge
    #external: true

volumes:
  rabbitmq_data:
