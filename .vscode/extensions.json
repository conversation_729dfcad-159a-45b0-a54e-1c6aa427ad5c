{
  // See https://go.microsoft.com/fwlink/?LinkId=827846 to learn about workspace recommendations.
  // Extension identifier format: ${publisher}.${name}. Example: vscode.csharp
  // List of extensions which should be recommended for users of this workspace.
  "recommendations": [
    "esbenp.prettier-vscode",
    "humao.rest-client",
    "vscode-icons-team.vscode-icons",
    "ms-azuretools.vscode-docker",
    "dbaeumer.vscode-eslint",
    "firsttris.vscode-jest-runner",
    "redhat.vscode-yaml",
    "vscode-icons-team.vscode-icons",
    "github.github-vscode-theme",
    "johnpapa.vscode-peacock"
  ],
  // List of extensions recommended by VS Code that should not be recommended for users of this workspace.
  "unwantedRecommendations": []
}
