import { registerAs } from '@nestjs/config';
import { KeycloakConnectConfig, TokenValidation } from 'nest-keycloak-connect';

export default registerAs('auth', () => {
  const config: KeycloakConnectConfig = {
    clientId: process.env.AUTH_CLIENT_ID,
    bearerOnly: true, // Apenas valida tokens, sem fluxo de login
    serverUrl: process.env.AUTH_URL,
    realm: process.env.AUTH_REALM,
    secret: process.env.AUTH_CLIENT_SECRET,
    tokenValidation: TokenValidation.OFFLINE,
    useNestLogger: true,
  };

  console.log(config);
  return config;
});
