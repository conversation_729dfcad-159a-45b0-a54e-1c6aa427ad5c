// Refs.: https://medium.com/nexton/how-to-establish-a-secure-connection-from-a-node-js-api-to-an-aws-rds-f79c5daa2ea5
// Refs.: https://docs.aws.amazon.com/AmazonRDS/latest/UserGuide/UsingWithRDS.SSL.html
import * as fs from 'fs';

import { registerAs } from '@nestjs/config';

export default registerAs('database', () => ({
  type: 'postgres',
  host: process.env.DB_HOST || 'localhost',
  port: +process.env.DB_PORT || 5432,
  username: process.env.DB_USER,
  password: process.env.DB_PASS,
  database: process.env.DB_NAME,
  entities: [`${__dirname}/../**/*.entity{.ts,.js}`],
  logging: process.env.NODE_ENV === 'development',
  migrations: [`${__dirname}/../../db/migrations/*{.ts,.js}`],
  migrationsRun: process.env.MIGRATIONS_RUN === 'true',
  synchronize: false,
  ssl:
    process.env.NODE_ENV != 'production'
      ? false
      : {
          ca: fs.readFileSync(`${__dirname}/../../db/certs/aws-rds-us-east-1-bundle.pem`).toString(),
        },
}));
