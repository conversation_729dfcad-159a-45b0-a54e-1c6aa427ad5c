import { Injectable, CanActivate, ExecutionContext } from '@nestjs/common';
import { Reflector } from '@nestjs/core';

@Injectable()
export class KeycloakRoleGuard implements CanActivate {
  constructor(private readonly reflector: Reflector) {}

  canActivate(context: ExecutionContext): boolean {
    const requiredRolesObject = this.reflector.get<{ roles: string[] }>('roles', context.getHandler());
    const requiredRoles = requiredRolesObject?.roles || [];

    if (!Array.isArray(requiredRoles) || requiredRoles.length === 0) {
      return true;
    }

    const request = context.switchToHttp().getRequest();
    const userRoles = request.user?.realm_access?.roles || [];

    return requiredRoles.some((role) => userRoles.includes(role));
  }
}
