import { Controller, Get } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { Public } from 'nest-keycloak-connect';

/**
 * Controller principal da aplicação
 * Responsável por endpoints básicos como healthcheck e status da API
 */
@Controller()
@ApiTags('Health Check')
export class AppController {
  /**
   * Endpoint de verificação de status da API
   * Não requer autenticação e retorna 'OK' se a API estiver funcionando
   */
  @Get()
  @Public()
  @ApiOperation({
    summary: 'Verificar status da API',
    description: 'Endpoint para verificar se a API está online e respondendo corretamente',
  })
  @ApiResponse({
    status: 200,
    description: 'API está funcionando normalmente',
    schema: {
      type: 'string',
      example: 'OK',
    },
  })
  status(): string {
    return 'OK';
  }
}
