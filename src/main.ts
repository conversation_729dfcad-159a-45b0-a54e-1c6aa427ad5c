import { Logger, ValidationPipe } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { NestFactory } from '@nestjs/core';
import { NestExpressApplication } from '@nestjs/platform-express';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import * as compression from 'compression';
import helmet from 'helmet';
import { AppModule } from './app.module';
import { BusinessExceptionFilter } from './core/infrastructure/common/filters/business-exception.filter';
import { TypeORMErrorFilter } from './core/infrastructure/common/filters/type-orm-error.filter';
import './register-aliases';

async function bootstrap() {
  const logger = new Logger('Bootstrap');

  // Criação da aplicação com tipo específico
  const app = await NestFactory.create<NestExpressApplication>(AppModule, {
    logger: ['error', 'warn', 'log', 'debug', 'verbose'],
  });

  // Configuração do ConfigService
  const configService = app.get(ConfigService);
  const port = configService.get<number>('config.port', 3000);
  const environment = configService.get<string>('config.nodenv', 'development');

  // Middlewares de segurança e performance
  app.use(helmet());
  app.use(compression());
  app.enableCors({
    allowedHeaders: '*',
    origin: configService.get<string>('CORS_ORIGIN', '*'),
    credentials: true,
  });

  // Pipes e filtros globais
  app.useGlobalPipes(
    new ValidationPipe({
      transform: true,
      whitelist: true,
      forbidNonWhitelisted: true,
      transformOptions: {
        enableImplicitConversion: true,
      },
    }),
  );
  app.useGlobalFilters(new TypeORMErrorFilter(), new BusinessExceptionFilter());

  // Prefixo global da API
  app.setGlobalPrefix('api/v1', {
    exclude: ['/'],
  });

  // Configuração do Swagger
  const config = new DocumentBuilder()
    .setTitle('Develop Platform API')
    .setDescription('API documentation for the Develop Platform')
    .setVersion('1.0')
    .addBearerAuth()
    .build();
  const document = SwaggerModule.createDocument(app, config);
  SwaggerModule.setup('api/docs', app, document);

  // Inicialização do servidor
  await app.listen(port);
  logger.log(`Application is running on: ${await app.getUrl()}`);
  logger.log(`Environment: ${environment}`);
  logger.log(`Documentation available at: ${await app.getUrl()}/api/docs`);
}

bootstrap().catch((error) => {
  new Logger('Bootstrap').error('Failed to start application', error);
});
