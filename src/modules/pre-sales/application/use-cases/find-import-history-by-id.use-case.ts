import { BadRequestException, Injectable, Inject } from '@nestjs/common';
import { PreSaleImportHistory } from '../../domain/entities/pre-sale-import-history';
import { PreSaleImportHistoryVehicle } from '../../domain/entities/pre-sale-import-history-vehicle';
import { IPreSaleImportHistoryRepository } from '../../domain/repositories/pre-sale-import-history.repository.interface';
import { PreSaleImportHistoryResponseDto, PreSaleImportHistoryVehicleResponseDto } from '../../presentation/dtos/pre-sale-import-history-response.dto';

@Injectable()
export class FindImportHistoryByIdUseCase {
  constructor(
    @Inject('IPreSaleImportHistoryRepository')
    private readonly importHistoryRepository: IPreSaleImportHistoryRepository,
  ) {}

  async execute(id: string): Promise<PreSaleImportHistoryResponseDto> {
    const importHistory = await this.importHistoryRepository.findById(id);
    if (!importHistory) {
      throw new BadRequestException('Importação não encontrada');
    }
    return this.mapToResponse(importHistory);
  }

  private mapToResponse(importHistory: PreSaleImportHistory): PreSaleImportHistoryResponseDto {
    const response = new PreSaleImportHistoryResponseDto();
    response.id = importHistory.id;
    response.originalFileName = importHistory.originalFileName;
    response.filePath = importHistory.filePath;
    response.status = importHistory.status;
    response.totalRecords = importHistory.totalRecords;
    response.processedRecords = importHistory.processedRecords;
    response.successCount = importHistory.successCount;
    response.errorCount = importHistory.errorCount;
    response.alreadyRegisteredCount = importHistory.alreadyRegisteredCount;
    response.progress = importHistory.progress;

    return response;
  }

  private mapVehicleToResponse(vehicle: PreSaleImportHistoryVehicle): PreSaleImportHistoryVehicleResponseDto {
    const response = new PreSaleImportHistoryVehicleResponseDto();
    response.id = vehicle.id;
    response.importHistoryId = vehicle.importHistoryId;
    response.vehicleId = vehicle.vehicleId;
    response.lineNumber = vehicle.lineNumber;
    response.status = vehicle.status;
    response.errorMessage = vehicle.errorMessage;
    return response;
  }
}
