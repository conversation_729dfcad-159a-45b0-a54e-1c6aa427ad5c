import { Inject, Injectable } from '@nestjs/common';
import { Paginated, PaginateQuery } from 'nestjs-paginate';
import { IPreSaleImportHistoryRepository } from '../../domain/repositories/pre-sale-import-history.repository.interface';
import { PreSaleImportHistoryEntity } from '../../infrastructure/entities/pre-sale-import-history.entity';
@Injectable()
export class FindAllImportHistoriesUseCase {
  constructor(
    @Inject('IPreSaleImportHistoryRepository')
    private readonly importHistoryRepository: IPreSaleImportHistoryRepository,
  ) {}

  async execute(query: PaginateQuery): Promise<Paginated<PreSaleImportHistoryEntity>> {
    return await this.importHistoryRepository.findAll(query);
  }
}
