import { Injectable, Logger } from '@nestjs/common';
import * as XLSX from 'xlsx';
import { InvalidImportFileException } from '../../domain/exceptions/invalid-import-file.exception';
import { FindImportHistoryByIdUseCase } from './find-import-history-by-id.use-case';
import { MissingRequiredHeadersException } from '@/modules/pre-sales/domain/exceptions/missing-required-headers.exception';
import { FindImportHistoryVehicleByPlateChassisRenavamUseCase } from './find-import-history-vehicle-by-plate-chassis-renavam.use-case';
import { VehicleFine } from '@/modules/vehicle-fines/domain/entities/vehicle-fine.entity';
import { VehicleRecall } from '@/modules/vehicle-recalls/domain/entities/vehicle-recall.entity';
import { VehicleDebit } from '@/modules/vehicle-debits/domain/entities/vehicle-debit.entity';
import { UpdateImportHistoryVehicleStatusUseCase } from './update-import-history-vehicle-status.use-case';
import { PreSaleImportHistoryVehicleStatus } from '@/modules/pre-sales/domain/entities/pre-sale-import-history-vehicle';
import { Vehicle } from '@/modules/vehicles/domain/vehicle.entity';
import { UpdateImportHistoryStatusUseCase } from '@/modules/pre-sales/application/use-cases/update-import-history-status.use-case';
import { PreSaleImportHistoryStatus } from '@/modules/pre-sales/domain/entities/pre-sale-import-history';
import { Renavam } from '@/modules/vehicles/domain/value-objects/renavam.vo';
import { Chassi } from '@/modules/vehicles/domain/value-objects/chassi';
import { LicensePlate } from '@/modules/vehicles/domain/value-objects/license-plate.vo';
import { AlreadyProcessingPreSaleException } from '@/modules/pre-sales/domain/exceptions/already-processing-pre-sale.exception';
import { ProcessImportHistoryResultVehicleUseCase } from '@/modules/pre-sales/application/use-cases/process-import-history-result-vehicle.use-case';

const FINES_REQUIRED_HEADERS = [
  'RENAVAM',
  'CHASSI',
  'PLACA',
  'MULTAS',
  'SITUAÇÃO',
  'DATA DA INFRAÇÃO',
  'HORA DA INFRAÇÃO',
  'ÓRGÃO AUTUADOR',
  'CÓD. DA INFRAÇÃO',
  'DESCRIÇÃO DA INFRAÇÃO',
  'LOCAL DA INFRAÇÃO',
  'VALOR',
  'VENCIMENTO',
  'CÓDIGO DE BARRAS',
] as const;

const VEHICLE_REQUIRED_HEADERS = [
  'PLACA',
  'CHASSI',
  'RENAVAM',
  'SITUAÇÃO VEICULO',
  'STATUS GRAVAME (BAIXADO OU ATIVO)',
  'PROPRIETÁRIO',
  'CPF/CNPJ',
  'ULTIMO LAUDO CSV',
  'PRAZO PARA REGULARIZAÇÃO',
  'MOTIVO BLOQUEIO',
  'SITUAÇÃO GRAVAME',
  'LICENCIAMENTO',
  'ULTIMO LICENCIAMENTO',
  'RECALL',
  'DESCRIÇÃO',
  'DATA REGISTRO',
  'DATA LIMITE',
  'SITUAÇÃO RECALL',
  'IPVA',
  'UF',
] as const;

type FINES_HEADERS = (typeof FINES_REQUIRED_HEADERS)[number];
type VEHICLE_HEADERS = (typeof VEHICLE_REQUIRED_HEADERS)[number];
type FineRawData = Map<FINES_HEADERS, string>;
type VehicleFinesData = FineRawData[];

type Input = {
  file: Express.Multer.File;
  preSaleImportHistoryId: string;
};

@Injectable()
export class ProcessResultImportFileUseCase {
  private static parseOptionalDate(value: string) {
    if (!value || value === 'N/C' || value === '-') {
      return undefined;
    }
    return new Date(value);
  }

  private static parseNullableValue(value: string) {
    if (!value || value === 'N/C' || value === '-') {
      return null;
    }

    return value;
  }

  private readonly logger = new Logger(ProcessResultImportFileUseCase.name);
  private currentProcessingId: string | undefined = undefined;

  constructor(
    private readonly updateImportHistoryStatusUseCase: UpdateImportHistoryStatusUseCase,
    private readonly findImportHistoryByIdUseCase: FindImportHistoryByIdUseCase,
    private readonly findImportHistoryVehicleByPlateChassisRenavamUseCase: FindImportHistoryVehicleByPlateChassisRenavamUseCase,
    private readonly updateImportHistoryVehicleStatusUseCase: UpdateImportHistoryVehicleStatusUseCase,
    private readonly processImportHistoryResultVehicleUseCase: ProcessImportHistoryResultVehicleUseCase,
  ) {}

  async execute({ file, preSaleImportHistoryId }: Input) {
    if (this.currentProcessingId) {
      throw new AlreadyProcessingPreSaleException(this.currentProcessingId);
    }

    const importHistory = await this.findImportHistoryByIdUseCase.execute(preSaleImportHistoryId);
    this.currentProcessingId = preSaleImportHistoryId;
    this.logger.debug(`Iniciando processamento do resultado do lote ID: ${preSaleImportHistoryId}`);

    // Marca o lote como em processamento
    await this.updateImportHistoryStatusUseCase.execute({
      importHistoryId: preSaleImportHistoryId,
      status: PreSaleImportHistoryStatus.PROCESSING,
    });

    // Recupera veículos e multas
    const [vehiclesRows, finesRows] = this.processFile(file);
    const finesMap = this.createFinesMap(finesRows);

    // Processa cada veículo de forma individual
    await this.processVehicles(vehiclesRows, preSaleImportHistoryId, finesMap);

    if (importHistory.status === PreSaleImportHistoryStatus.STARTED) {
      await this.updateImportHistoryStatusUseCase.execute({
        importHistoryId: preSaleImportHistoryId,
        status: PreSaleImportHistoryStatus.COMPLETED,
      });
    }

    this.logger.debug(`Processamento do resultado do lote ID: ${preSaleImportHistoryId} finalizado.`);
    this.currentProcessingId = undefined;
  }

  private processFile(file: Express.Multer.File) {
    if (!file || !file.buffer) {
      throw new InvalidImportFileException('Arquivo não fornecido ou inválido');
    }

    let workbook: XLSX.WorkBook;
    try {
      // Lê cada pasta da planilha convertendo as datas para objetos javascript
      workbook = XLSX.read(file.buffer, { type: 'buffer', cellDates: true });
    } catch (error) {
      throw new InvalidImportFileException('Erro ao ler o arquivo Excel. Verifique se o arquivo está corrompido ou vazio.');
    }

    if (!workbook.SheetNames || workbook.SheetNames.length === 0) {
      throw new InvalidImportFileException('O arquivo Excel não contém nenhuma planilha');
    }

    const vehiclesWorksheet = workbook.Sheets[workbook.SheetNames[0]];
    const finesWorksheet = workbook.Sheets[workbook.SheetNames[1]];

    const vehiclesRows = this.getWorksheetRows(vehiclesWorksheet, 'veículos', VEHICLE_REQUIRED_HEADERS, true);
    const finesRows = this.getWorksheetRows(finesWorksheet, 'multas', FINES_REQUIRED_HEADERS, false);

    return [vehiclesRows, finesRows];
  }

  private getWorksheetRows(worksheet: XLSX.WorkSheet, worksheetName: string, requiredHeaders: readonly string[], requiredWorkSheet: boolean): any[] {
    if (!worksheet) {
      if (requiredWorkSheet) {
        throw new InvalidImportFileException(`A página ${worksheetName} está vazia ou corrompida`);
      }
      return [];
    }

    // Recupera as linhas, pulando as em branco e sem realizar formatação no texto
    // (raw usado em conjunto com cellDates para converter as células do tipo data "d" - em objetos data)
    const rows = XLSX.utils.sheet_to_json<string[]>(worksheet, { header: 1, defval: '', blankrows: false, raw: true });

    if (requiredWorkSheet && (!Array.isArray(rows) || rows.length < 2)) {
      throw new InvalidImportFileException(
        `A página de ${worksheetName} não contém dados suficientes. É necessário pelo menos um cabeçalho e uma linha de dados.`,
      );
    }

    const headers = rows[0];
    if (!Array.isArray(headers) || headers.length === 0) {
      throw new InvalidImportFileException(`A página ${worksheetName} não contém cabeçalhos válidos`);
    }

    const missingHeaders = requiredHeaders.filter((header) => !headers.includes(header));
    if (missingHeaders.length > 0) {
      throw new MissingRequiredHeadersException(missingHeaders);
    }

    return rows;
  }

  private async processVehicles(rows: any[], importHistoryId: string, finesMap: Map<string, VehicleFinesData>) {
    const headers = rows[0] as string[];
    for (let i = 1; i < rows.length; i++) {
      const vehicleRow = rows[i];

      if (!Array.isArray(vehicleRow)) {
        continue;
      }

      const vehicleData: Map<VEHICLE_HEADERS, string> = new Map();
      VEHICLE_REQUIRED_HEADERS.map((header) => {
        const columnIndex = headers.indexOf(header);
        const value = columnIndex >= 0 ? vehicleRow[columnIndex] : '';
        vehicleData.set(header, value ? String(value).trim() : '');
      });

      // Processa cada linha da planilha de veículos, atualizando o veículo, criando débitos, recalls e multas
      await this.processVehicleRow(vehicleData, importHistoryId, i + 1, finesMap);
    }
  }

  private async processVehicleRow(
    vehicleData: Map<VEHICLE_HEADERS, string>,
    importHistoryId: string,
    rowNumber: number,
    finesMap: Map<string, VehicleFinesData>,
  ) {
    const plate = new LicensePlate(vehicleData.get('PLACA'));
    const chassis = new Chassi(vehicleData.get('CHASSI'));
    const renavam = new Renavam(vehicleData.get('RENAVAM'));

    const importHistoryVehicle = await this.findImportHistoryVehicleByPlateChassisRenavamUseCase.execute(
      importHistoryId,
      plate.getValue(),
      chassis.getValue(),
      renavam.getValue(),
    );

    if (!importHistoryVehicle) {
      this.logger.error(
        `Registro na planilha de veículos não localizado no histórico ${importHistoryId}. Linha #${rowNumber} - Placa ${plate.getValue()} - Chassi ${chassis.getValue()} - Renavam ${renavam.getValue()}`,
      );
      return;
    }

    if (importHistoryVehicle.status === PreSaleImportHistoryVehicleStatus.SUCCESS) {
      this.logger.debug(`Veículo da linha #${rowNumber} já processado com sucesso, pulando a operação.`);
      return;
    }

    try {
      const vehicleId = importHistoryVehicle.vehicleId;
      const vehicle = this.createUpdateVehicle(vehicleData, vehicleId);

      const licencing = this.createVehicleLicencing(vehicleData, vehicleId, importHistoryId, importHistoryVehicle.id, rowNumber);
      vehicle.addDebit(licencing);

      const recall = this.createVehicleRecall(vehicleData, vehicleId, importHistoryVehicle.id, rowNumber);
      vehicle.addRecall(recall);

      // Cria as multas para esse veículo
      const vehicleFinesData = finesMap.get(plate.getValue());
      if (vehicleFinesData?.length) {
        for (const vehicleFineData of vehicleFinesData) {
          const vehicleFine = this.createVehicleFine(vehicleId, importHistoryVehicle.id, vehicleFineData);
          vehicle.addFine(vehicleFine);
        }
      }

      // Atualiza o veículo, cria débitos, recalls, multas e atualiza o status do item do histórico
      await this.processImportHistoryResultVehicleUseCase.execute({
        vehicle,
        preSaleImportHistoryVehicleId: importHistoryVehicle.id,
      });
    } catch (e) {
      this.logger.error(`Erro ao processar veículo da linha ${rowNumber}`, e);

      await this.updateImportHistoryVehicleStatusUseCase.execute({
        importHistoryVehicleId: importHistoryVehicle.id,
        status: PreSaleImportHistoryVehicleStatus.ERROR,
      });
    }
  }

  private createUpdateVehicle(vehicleData: Map<VEHICLE_HEADERS, string>, vehicleId: string) {
    const plate = new LicensePlate(vehicleData.get('PLACA'));
    const chassis = new Chassi(vehicleData.get('CHASSI'));
    const renavam = new Renavam(vehicleData.get('RENAVAM'));

    const situation = vehicleData.get('SITUAÇÃO VEICULO');
    const gravameStatus = vehicleData.get('STATUS GRAVAME (BAIXADO OU ATIVO)');
    const owner = vehicleData.get('PROPRIETÁRIO');
    const ownerDocument = vehicleData.get('CPF/CNPJ');
    const gravameSituation = ProcessResultImportFileUseCase.parseNullableValue(vehicleData.get('SITUAÇÃO GRAVAME'));
    const lastLicensing = vehicleData.get('ULTIMO LICENCIAMENTO');
    const state = vehicleData.get('UF');
    const lastCsvReport = ProcessResultImportFileUseCase.parseOptionalDate(vehicleData.get('ULTIMO LAUDO CSV'));
    const regularizationDeadline = ProcessResultImportFileUseCase.parseOptionalDate(vehicleData.get('PRAZO PARA REGULARIZAÇÃO'));

    return Vehicle.create({
      id: vehicleId,
      situation,
      gravameStatus,
      gravameSituation,
      lastLicensing,
      lastCsvReport,
      regularizationDeadline,
      owner,
      ownerDocument,
      licensePlate: plate.getValue(),
      chassi: chassis.getValue(),
      renavam: renavam.getValue(),
      state: state,
    });
  }

  private createVehicleLicencing(
    vehicleData: Map<VEHICLE_HEADERS, string>,
    vehicleId: string,
    importHistoryId: string,
    importHistoryVehicleId: string,
    rowNumber: number,
  ): VehicleDebit | undefined {
    const licencing = vehicleData.get('LICENCIAMENTO');
    if (!licencing || licencing === 'N/C') {
      this.logger.debug(`Sem informações de licenciamento para o veículo da linha ${rowNumber}.`);
      return;
    }

    const licencingValue = parseFloat(licencing);
    if (isNaN(licencingValue)) {
      this.logger.error(`Erro ao processar valor do licenciamento do veículo. Linha #${rowNumber}`);
      return;
    }

    return new VehicleDebit(
      vehicleId,
      `Licenciamento do veículo registrado no lote ${importHistoryId}`,
      licencingValue,
      null,
      'LICENCIAMENTO',
      null,
      importHistoryVehicleId,
    );
  }

  private createVehicleRecall(
    vehicleData: Map<VEHICLE_HEADERS, string>,
    vehicleId: string,
    preSaleImportHistoryVehicleId: string,
    rowNumber: number,
  ): VehicleRecall | undefined {
    const recallInfo = vehicleData.get('RECALL');

    if (!recallInfo || recallInfo === 'N/C') {
      this.logger.debug(`Sem informações de recall para o veículo da linha ${rowNumber}.`);
      return;
    }

    return VehicleRecall.create({
      vehicleId,
      description: vehicleData.get('DESCRIÇÃO'),
      recall: recallInfo,
      deadline: new Date(vehicleData.get('DATA LIMITE')),
      registrationDate: new Date(vehicleData.get('DATA REGISTRO')),
      status: vehicleData.get('SITUAÇÃO RECALL'),
      preSaleImportHistoryVehicleId,
    });
  }

  private createFinesMap(rows: any[]) {
    const headers = rows[0] as string[];
    const fines = new Map<string, VehicleFinesData>();

    for (let i = 1; i < rows.length; i++) {
      const fineRow = rows[i];

      if (!Array.isArray(fineRow)) {
        continue;
      }

      const fineData: FineRawData = new Map();
      FINES_REQUIRED_HEADERS.map((header) => {
        const columnIndex = headers.indexOf(header);
        const value = columnIndex >= 0 ? fineRow[columnIndex] : '';
        fineData.set(header, value ? String(value).trim() : '');
      });

      const vehiclePlate = fineData.get('PLACA');

      if (!fines.has(vehiclePlate)) {
        fines.set(vehiclePlate, [fineData]);
      } else {
        const vehicleFinesData = fines.get(vehiclePlate);
        vehicleFinesData.push(fineData);
        fines.set(vehiclePlate, vehicleFinesData);
      }
    }

    return fines;
  }

  private createVehicleFine(vehicleId: string, preSaleImportHistoryVehicleId: string, fineData: Map<FINES_HEADERS, string>) {
    const infractionTime = new Date(fineData.get('HORA DA INFRAÇÃO')).toTimeString().slice(0, 8);

    return VehicleFine.create({
      vehicleId,
      preSaleImportHistoryVehicleId,
      date: new Date(fineData.get('DATA DA INFRAÇÃO')),
      value: parseFloat(fineData.get('VALOR')),
      fineCode: fineData.get('MULTAS'),
      status: fineData.get('SITUAÇÃO'),
      time: infractionTime,
      enforcingAuthority: fineData.get('ÓRGÃO AUTUADOR'),
      infractionCode: fineData.get('CÓD. DA INFRAÇÃO'),
      description: fineData.get('DESCRIÇÃO DA INFRAÇÃO'),
      location: fineData.get('LOCAL DA INFRAÇÃO'),
      dueDate: ProcessResultImportFileUseCase.parseOptionalDate(fineData.get('VENCIMENTO')),
      barCode: ProcessResultImportFileUseCase.parseNullableValue(fineData.get('CÓDIGO DE BARRAS')),
    });
  }
}
