import { BadRequestException, Inject, Injectable } from '@nestjs/common';
import { PaginateQuery, Paginated } from 'nestjs-paginate';
import { IPreSaleImportHistoryRepository } from '../../domain/repositories/pre-sale-import-history.repository.interface';
import { PreSaleImportHistoryVehicleEntity } from '../../infrastructure/entities/pre-sale-import-history-vehicle.entity';

@Injectable()
export class FindVehiclesByImportHistoryIdUseCase {
  constructor(
    @Inject('IPreSaleImportHistoryRepository')
    private readonly importHistoryRepository: IPreSaleImportHistoryRepository,
  ) {}

  async execute(id: string, query: PaginateQuery): Promise<Paginated<PreSaleImportHistoryVehicleEntity>> {
    const importHistory = await this.importHistoryRepository.findById(id);
    if (!importHistory) {
      throw new BadRequestException('Importação não encontrada');
    }
    return await this.importHistoryRepository.findVehiclesByImportHistoryId(id, query);
  }
}
