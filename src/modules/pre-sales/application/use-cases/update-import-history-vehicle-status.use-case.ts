import { Inject, Injectable } from '@nestjs/common';
import { IPreSaleImportHistoryRepository } from '../../domain/repositories/pre-sale-import-history.repository.interface';
import { PreSaleImportHistoryVehicleStatus } from '@/modules/pre-sales/domain/entities/pre-sale-import-history-vehicle';

type Input = {
  importHistoryVehicleId: string;
  status: PreSaleImportHistoryVehicleStatus;
};

@Injectable()
export class UpdateImportHistoryVehicleStatusUseCase {
  constructor(
    @Inject('IPreSaleImportHistoryRepository')
    private readonly importHistoryRepository: IPreSaleImportHistoryRepository,
  ) {}

  async execute({ importHistoryVehicleId, status }: Input) {
    return this.importHistoryRepository.updateImportHistoryVehicleStatus(importHistoryVehicleId, status);
  }
}
