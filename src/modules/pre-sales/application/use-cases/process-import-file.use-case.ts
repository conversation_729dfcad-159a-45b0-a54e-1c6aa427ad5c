import { Inject, Injectable } from '@nestjs/common';
import * as XLSX from 'xlsx';
import { PreSaleImportHistoryVehicle } from '../../domain/entities/pre-sale-import-history-vehicle';
import { InvalidImportFileException } from '../../domain/exceptions/invalid-import-file.exception';
import { MissingRequiredHeadersException } from '../../domain/exceptions/missing-required-headers.exception';
import { IPreSaleImportHistoryRepository } from '../../domain/repositories/pre-sale-import-history.repository.interface';
import { PreSaleImportHistoryResponseDto } from '../../presentation/dtos/pre-sale-import-history-response.dto';
import { CreateImportHistoryUseCase } from './create-import-history.use-case';
import { CreateVehicleUseCase } from '@/modules/vehicles/application/create-vehicle.use-case';
import { FindVehicleByPlateChassisRenavamUseCase } from '@/modules/vehicles/application/find-vehicle-by-plate-chassis-renavam.use-case';
import { Vehicle } from '@/modules/vehicles/domain/vehicle.entity';
import { Renavam } from '@/modules/vehicles/domain/value-objects/renavam.vo';
import { Chassi } from '@/modules/vehicles/domain/value-objects/chassi';
import { LicensePlate } from '@/modules/vehicles/domain/value-objects/license-plate.vo';
interface ProcessImportFileInput {
  file: Express.Multer.File;
}

@Injectable()
export class ProcessImportFileUseCase {
  constructor(
    @Inject('IPreSaleImportHistoryRepository')
    private readonly importHistoryRepository: IPreSaleImportHistoryRepository,
    private readonly createVehicleUseCase: CreateVehicleUseCase,
    private readonly findVehicleByPlateChassisRenavamUseCase: FindVehicleByPlateChassisRenavamUseCase,
    private readonly createImportHistoryUseCase: CreateImportHistoryUseCase,
  ) {}

  async execute({ file }: ProcessImportFileInput): Promise<PreSaleImportHistoryResponseDto> {
    if (!file || !file.buffer) {
      throw new InvalidImportFileException('Arquivo não fornecido ou inválido');
    }

    let workbook: XLSX.WorkBook;
    try {
      workbook = XLSX.read(file.buffer, { type: 'buffer' });
    } catch (error) {
      throw new InvalidImportFileException('Erro ao ler o arquivo Excel. Verifique se o arquivo está corrompido ou vazio.');
    }

    if (!workbook.SheetNames || workbook.SheetNames.length === 0) {
      throw new InvalidImportFileException('O arquivo Excel não contém nenhuma planilha');
    }

    const worksheet = workbook.Sheets[workbook.SheetNames[0]];
    if (!worksheet) {
      throw new InvalidImportFileException('A planilha está vazia ou corrompida');
    }

    const rows = XLSX.utils.sheet_to_json(worksheet, { header: 1, defval: '', blankrows: false });

    if (!Array.isArray(rows) || rows.length < 2) {
      throw new InvalidImportFileException('O arquivo não contém dados suficientes. É necessário pelo menos um cabeçalho e uma linha de dados.');
    }

    const headers = rows[0] as string[];
    if (!Array.isArray(headers) || headers.length === 0) {
      throw new InvalidImportFileException('O arquivo não contém cabeçalhos válidos');
    }

    const requiredHeaders = ['RENAVAM', 'CHASSI', 'PLACA', 'ESTADO'];
    this.validateHeaders(headers, requiredHeaders);

    const importHistory = await this.createImportHistoryUseCase.execute({
      originalFileName: file.originalname,
      filePath: `memory://${file.originalname}`,
      totalRecords: rows.length - 1,
    });

    // Processar cada linha
    for (let i = 1; i < rows.length; i++) {
      const row = rows[i] as any[];
      if (!Array.isArray(row)) {
        continue; // Pula linhas inválidas
      }

      const [renavam, chassi, licensePlate, estado] = requiredHeaders.map((header) => {
        const value = row[headers.indexOf(header)];
        return value ? String(value).trim() : '';
      });

      // Validar dados obrigatórios
      if (!renavam || !chassi || !licensePlate || !estado) {
        const vehicle = new PreSaleImportHistoryVehicle(importHistory.id, i);
        vehicle.markAsError('Dados obrigatórios ausentes');
        await this.importHistoryRepository.createVehicle(vehicle);
        continue;
      }

      // Procurar veículo existente por qualquer identificador
      const existingVehicle = await this.findVehicleByPlateChassisRenavamUseCase.execute(licensePlate, chassi, renavam);

      if (existingVehicle) {
        const vehicle = new PreSaleImportHistoryVehicle(importHistory.id, i);

        // Verificar divergências nos identificadores
        const divergentFields = this.checkIdentifierDivergences(existingVehicle, {
          renavam: new Renavam(renavam).getValue(),
          chassi: new Chassi(chassi).getValue(),
          placa: new LicensePlate(licensePlate).getValue(),
        });

        if (divergentFields.length > 0) {
          vehicle.markAsError(`Divergências encontradas nos campos: ${divergentFields.join(', ')}`);
        } else {
          vehicle.markAsStarted(existingVehicle.id);
        }

        await this.importHistoryRepository.createVehicle(vehicle);
        continue;
      }

      // Criar novo veículo
      try {
        const newVehicle = new Vehicle({ renavam, chassi, licensePlate: licensePlate, state: estado });
        const createdVehicle = await this.createVehicleUseCase.execute({
          renavam: newVehicle.getRenavam(),
          chassi: newVehicle.getChassis(),
          numberPlate: newVehicle.getLicensePlate(),
          state: newVehicle.getState(),
        });

        const vehicle = new PreSaleImportHistoryVehicle(importHistory.id, i);
        vehicle.markAsStarted(createdVehicle.id);
        await this.importHistoryRepository.createVehicle(vehicle);
      } catch (error) {
        const vehicle = new PreSaleImportHistoryVehicle(importHistory.id, i);
        vehicle.markAsError(`Erro ao criar veículo: ${error.message}`);
        await this.importHistoryRepository.createVehicle(vehicle);
      }
    }

    return importHistory;
  }

  private validateHeaders(headers: string[], requiredHeaders: string[]): void {
    const missingHeaders = requiredHeaders.filter((header) => !headers.includes(header));

    if (missingHeaders.length > 0) {
      throw new MissingRequiredHeadersException(missingHeaders);
    }
  }

  private checkIdentifierDivergences(existingVehicle: any, newData: { renavam: string; chassi: string; placa: string }): string[] {
    const divergences: string[] = [];

    if (existingVehicle.renavam && existingVehicle.renavam !== newData.renavam) {
      console.log('RENAVAM', existingVehicle.renavam, newData.renavam);
      divergences.push('RENAVAM');
    }
    if (existingVehicle.chassi && existingVehicle.chassi !== newData.chassi) {
      divergences.push('CHASSI');
    }
    if (existingVehicle.placa && existingVehicle.placa !== newData.placa) {
      divergences.push('PLACA');
    }

    return divergences;
  }
}
