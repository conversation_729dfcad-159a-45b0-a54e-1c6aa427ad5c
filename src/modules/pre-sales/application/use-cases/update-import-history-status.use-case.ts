import { Inject, Injectable } from '@nestjs/common';
import { IPreSaleImportHistoryRepository } from '@/modules/pre-sales/domain/repositories/pre-sale-import-history.repository.interface';
import { PreSaleImportHistoryStatus } from '@/modules/pre-sales/domain/entities/pre-sale-import-history';

type Input = {
  importHistoryId: string;
  status: PreSaleImportHistoryStatus;
};

@Injectable()
export class UpdateImportHistoryStatusUseCase {
  constructor(
    @Inject('IPreSaleImportHistoryRepository')
    private readonly importHistoryRepository: IPreSaleImportHistoryRepository,
  ) {}

  execute({ importHistoryId, status }: Input) {
    return this.importHistoryRepository.update(importHistoryId, { status });
  }
}
