import { Inject, Injectable } from '@nestjs/common';
import { IPreSaleImportHistoryRepository } from '../../domain/repositories/pre-sale-import-history.repository.interface';

@Injectable()
export class FindImportHistoryVehicleByPlateChassisRenavamUseCase {
  constructor(
    @Inject('IPreSaleImportHistoryRepository')
    private readonly importHistoryRepository: IPreSaleImportHistoryRepository,
  ) {}

  execute(importHistoryId: string, plate: string, chassi: string, renavam: string) {
    return this.importHistoryRepository.findImportHistoryVehicleByPlateChassiRenavam(importHistoryId, plate, chassi, renavam);
  }
}
