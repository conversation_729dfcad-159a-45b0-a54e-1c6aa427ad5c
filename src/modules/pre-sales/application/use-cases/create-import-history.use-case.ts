import { Injectable, Inject } from '@nestjs/common';
import { PreSaleImportHistory } from '../../domain/entities/pre-sale-import-history';
import { PreSaleImportHistoryVehicle } from '../../domain/entities/pre-sale-import-history-vehicle';
import { IPreSaleImportHistoryRepository } from '../../domain/repositories/pre-sale-import-history.repository.interface';
import { PreSaleImportHistoryResponseDto, PreSaleImportHistoryVehicleResponseDto } from '../../presentation/dtos/pre-sale-import-history-response.dto';

interface CreateImportHistoryInput {
  originalFileName: string;
  filePath: string;
  totalRecords: number;
}

@Injectable()
export class CreateImportHistoryUseCase {
  constructor(
    @Inject('IPreSaleImportHistoryRepository')
    private readonly importHistoryRepository: IPreSaleImportHistoryRepository,
  ) {}

  async execute(input: CreateImportHistoryInput): Promise<PreSaleImportHistoryResponseDto> {
    const importHistory = new PreSaleImportHistory(input.originalFileName, input.filePath);
    importHistory.totalRecords = input.totalRecords;

    const created = await this.importHistoryRepository.create(importHistory);
    return this.mapToResponse(created);
  }

  private mapToResponse(importHistory: PreSaleImportHistory): PreSaleImportHistoryResponseDto {
    const response = new PreSaleImportHistoryResponseDto();
    response.id = importHistory.id;
    response.originalFileName = importHistory.originalFileName;
    response.filePath = importHistory.filePath;
    response.status = importHistory.status;
    response.totalRecords = importHistory.totalRecords;
    response.processedRecords = importHistory.processedRecords;
    response.successCount = importHistory.successCount;
    response.errorCount = importHistory.errorCount;
    response.alreadyRegisteredCount = importHistory.alreadyRegisteredCount;
    response.progress = importHistory.progress;

    return response;
  }

  private mapVehicleToResponse(vehicle: PreSaleImportHistoryVehicle): PreSaleImportHistoryVehicleResponseDto {
    const response = new PreSaleImportHistoryVehicleResponseDto();
    response.id = vehicle.id;
    response.importHistoryId = vehicle.importHistoryId;
    response.vehicleId = vehicle.vehicleId;
    response.lineNumber = vehicle.lineNumber;
    response.status = vehicle.status;
    response.errorMessage = vehicle.errorMessage;
    return response;
  }
}
