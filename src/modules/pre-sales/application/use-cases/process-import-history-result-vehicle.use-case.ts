import { Inject, Injectable } from '@nestjs/common';
import { PreSaleImportHistoryResultRepository } from '@/modules/pre-sales/infrastructure/repositories/pre-sale-import-history-result.repository';
import { Vehicle } from '@/modules/vehicles/domain/vehicle.entity';

type Input = {
  vehicle: Vehicle;
  preSaleImportHistoryVehicleId: string;
};

@Injectable()
export class ProcessImportHistoryResultVehicleUseCase {
  constructor(@Inject('IPreSaleImportHistoryResultRepository') private readonly preSaleImportHistoryResultRepository: PreSaleImportHistoryResultRepository) {}

  async execute({ vehicle, preSaleImportHistoryVehicleId }: Input) {
    return this.preSaleImportHistoryResultRepository.processVehicle(vehicle, preSaleImportHistoryVehicleId);
  }
}
