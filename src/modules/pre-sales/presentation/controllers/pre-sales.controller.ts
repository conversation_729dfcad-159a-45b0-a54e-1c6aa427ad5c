import { Controller, Get, Param, Post, UploadedFile, UseInterceptors } from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { ApiBody, ApiConsumes, ApiOperation, ApiParam, ApiResponse, ApiTags } from '@nestjs/swagger';
import { plainToInstance } from 'class-transformer';
import { Paginate, PaginatedSwaggerDocs, PaginateQuery } from 'nestjs-paginate';
import { FindAllImportHistoriesUseCase } from '../../application/use-cases/find-all-import-histories.use-case';
import { FindImportHistoryByIdUseCase } from '../../application/use-cases/find-import-history-by-id.use-case';
import { FindVehiclesByImportHistoryIdUseCase } from '../../application/use-cases/find-vehicles-by-import-history-id.use-case';
import { ProcessImportFileUseCase } from '../../application/use-cases/process-import-file.use-case';
import { PRE_SALE_IMPORT_HISTORY_PAGINATE_CONFIG, PRE_SALE_IMPORT_HISTORY_VEHICLE_PAGINATE_CONFIG } from '../../infrastructure/pre-sale.query';
import { PreSaleImportHistoryResponseDto, PreSaleImportHistoryVehicleResponseDto } from '../dtos/pre-sale-import-history-response.dto';
import { PreSaleImportHistoryUploadDto } from '../dtos/pre-sale-import-history-upload.dto';
import { PreSaleImportHistoryResultUploadDto } from '../dtos/pre-sale-import-history-result-upload.dto';
import { ProcessResultImportFileUseCase } from '../../application/use-cases/process-result-import-file.use-case';

@ApiTags('Pré-vendas')
@Controller('pre-sales')
export class PreSalesController {
  constructor(
    private readonly processImportFileUseCase: ProcessImportFileUseCase,
    private readonly findAllImportHistoriesUseCase: FindAllImportHistoriesUseCase,
    private readonly findImportHistoryByIdUseCase: FindImportHistoryByIdUseCase,
    private readonly findVehiclesByImportHistoryIdUseCase: FindVehiclesByImportHistoryIdUseCase,
    private readonly processResultImportFileUseCase: ProcessResultImportFileUseCase,
  ) {}

  @Post('import')
  @ApiOperation({ summary: 'Importar veículos de um arquivo Excel' })
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    type: PreSaleImportHistoryUploadDto,
  })
  @ApiResponse({
    status: 201,
    description: 'Importação iniciada com sucesso',
    type: PreSaleImportHistoryResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Arquivo inválido ou ausente',
  })
  @UseInterceptors(FileInterceptor('file'))
  async importVehicles(@UploadedFile() file: Express.Multer.File): Promise<PreSaleImportHistoryResponseDto> {
    return this.processImportFileUseCase.execute({ file });
  }

  @Post(':id/import-result')
  @ApiOperation({ summary: 'Importar o resultado de uma consulta pré-vendas em um arquivo Excel' })
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    type: PreSaleImportHistoryResultUploadDto,
  })
  @ApiResponse({
    status: 201,
    description: 'Processamento iniciado com sucesso',
    type: PreSaleImportHistoryResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'Arquivo inválido ou ausente',
  })
  @UseInterceptors(FileInterceptor('file'))
  async processPreSalesResult(@Param('id') id: string, @UploadedFile() file: Express.Multer.File): Promise<void> {
    return this.processResultImportFileUseCase.execute({ file, preSaleImportHistoryId: id });
  }

  @Get('')
  @ApiOperation({ summary: 'Listar todas as importações de pré-vendas' })
  @PaginatedSwaggerDocs(PreSaleImportHistoryResponseDto, PRE_SALE_IMPORT_HISTORY_PAGINATE_CONFIG)
  async findAllImportHistory(
    @Paginate()
    query: PaginateQuery,
  ) {
    const result = await this.findAllImportHistoriesUseCase.execute(query);
    return {
      ...result,
      data: result.data.map((history) => plainToInstance(PreSaleImportHistoryResponseDto, history) as any),
    };
  }

  @Get(':id')
  @ApiOperation({ summary: 'Buscar uma importação específica de pré-vendas' })
  @ApiParam({
    name: 'id',
    description: 'ID da importação',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  @ApiResponse({
    status: 200,
    description: 'Importação encontrada',
    type: PreSaleImportHistoryResponseDto,
  })
  @ApiResponse({
    status: 404,
    description: 'Importação não encontrada',
  })
  async findImportHistoryById(@Param('id') id: string): Promise<PreSaleImportHistoryResponseDto> {
    return this.findImportHistoryByIdUseCase.execute(id);
  }

  @Get(':id/vehicles')
  @ApiOperation({ summary: 'Buscar veículos de uma importação de pré-vendas' })
  @PaginatedSwaggerDocs(PreSaleImportHistoryVehicleResponseDto, PRE_SALE_IMPORT_HISTORY_VEHICLE_PAGINATE_CONFIG)
  async findVehiclesByImportHistoryId(
    @Param('id') id: string,
    @Paginate()
    query: PaginateQuery,
  ) {
    const result = await this.findVehiclesByImportHistoryIdUseCase.execute(id, query);
    return {
      ...result,
      data: result.data.map((vehicle) => plainToInstance(PreSaleImportHistoryVehicleResponseDto, vehicle) as any),
    };
  }
}
