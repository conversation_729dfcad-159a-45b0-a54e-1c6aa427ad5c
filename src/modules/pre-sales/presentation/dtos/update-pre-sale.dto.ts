import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString, IsArray } from 'class-validator';

export class UpdatePreSaleDto {
  @ApiProperty({ example: '123e4567-e89b-12d3-a456-426614174000', required: false })
  @IsString()
  @IsOptional()
  clientId?: string;

  @ApiProperty({ example: ['123e4567-e89b-12d3-a456-426614174000'], required: false })
  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  vehicleIds?: string[];

  @ApiProperty({ example: 'Observações sobre a pré-venda', required: false })
  @IsString()
  @IsOptional()
  observations?: string;
}
