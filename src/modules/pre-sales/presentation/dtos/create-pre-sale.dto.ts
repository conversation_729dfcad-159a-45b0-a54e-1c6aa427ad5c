import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsArray, IsOptional } from 'class-validator';

export class CreatePreSaleDto {
  @ApiProperty({ example: '123e4567-e89b-12d3-a456-426614174000' })
  @IsString()
  clientId: string;

  @ApiProperty({ example: ['123e4567-e89b-12d3-a456-426614174000'] })
  @IsArray()
  @IsString({ each: true })
  vehicleIds: string[];

  @ApiProperty({ example: 'Observações sobre a pré-venda', required: false })
  @IsString()
  @IsOptional()
  observations?: string;
}
