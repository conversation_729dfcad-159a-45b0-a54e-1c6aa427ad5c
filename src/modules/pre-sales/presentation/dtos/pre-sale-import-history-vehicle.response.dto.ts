import { ApiProperty } from '@nestjs/swagger';
import { PreSaleImportHistoryVehicleStatus } from '../../domain/entities/pre-sale-import-history-vehicle';

export class PreSaleImportHistoryVehicleResponseDto {
  @ApiProperty()
  id: string;

  @ApiProperty()
  importHistoryId: string;

  @ApiProperty({ required: false })
  vehicleId?: string;

  @ApiProperty()
  lineNumber: number;

  @ApiProperty({ enum: PreSaleImportHistoryVehicleStatus })
  status: PreSaleImportHistoryVehicleStatus;

  @ApiProperty({ required: false })
  errorMessage?: string;

  @ApiProperty()
  originalRenavam: string;

  @ApiProperty()
  originalChassi: string;

  @ApiProperty()
  originalPlaca: string;

  @ApiProperty()
  originalEstado: string;

  @ApiProperty()
  createdAt: Date;

  @ApiProperty()
  updatedAt: Date;
}
