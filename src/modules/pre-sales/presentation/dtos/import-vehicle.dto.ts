import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsNotEmpty, Length, Matches } from 'class-validator';

export class ImportVehicleDto {
  @ApiProperty({ description: 'Vehicle license plate' })
  @IsString()
  @IsNotEmpty()
  @Length(7, 7)
  @Matches(/^[A-Z]{3}[0-9][0-9A-Z][0-9]{2}$/, {
    message: 'Invalid license plate format',
  })
  placa: string;

  @ApiProperty({ description: 'Vehicle chassis number' })
  @IsString()
  @IsNotEmpty()
  @Length(17, 17)
  @Matches(/^[A-HJ-NPR-Z0-9]{17}$/, {
    message: 'Invalid chassis format',
  })
  chassis: string;

  @ApiProperty({ description: 'Vehicle Renavam number' })
  @IsString()
  @IsNotEmpty()
  @Length(11, 11)
  @Matches(/^[0-9]{11}$/, {
    message: 'Invalid Renavam format',
  })
  renavam: string;

  @ApiProperty({ description: 'Vehicle state (UF)' })
  @IsString()
  @IsNotEmpty()
  @Length(2, 2)
  @Matches(/^[A-Z]{2}$/, {
    message: 'Invalid state format',
  })
  estado: string;
}
