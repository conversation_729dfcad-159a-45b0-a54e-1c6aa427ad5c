import { ApiProperty } from '@nestjs/swagger';
import { PreSaleImportHistoryStatus } from '../../domain/entities/pre-sale-import-history';
import { PreSaleImportHistoryVehicleStatus } from '../../domain/entities/pre-sale-import-history-vehicle';
import { PreSaleImportHistoryErrorDto } from './pre-sale-import-history-error.dto';

export class PreSaleImportHistoryVehicleResponseDto {
  @ApiProperty({ example: '123e4567-e89b-12d3-a456-426614174000' })
  id: string;

  @ApiProperty({ example: '123e4567-e89b-12d3-a456-426614174000' })
  importHistoryId: string;

  @ApiProperty({ example: '123e4567-e89b-12d3-a456-426614174000', required: false })
  vehicleId?: string;

  @ApiProperty({ example: 1 })
  lineNumber: number;

  @ApiProperty({ enum: PreSaleImportHistoryVehicleStatus })
  status: PreSaleImportHistoryVehicleStatus;

  @ApiProperty({ example: 'Invalid chassis number', required: false })
  errorMessage?: string;

  @ApiProperty({ example: 'ABC1234' })
  originalPlaca: string;

  @ApiProperty({ example: '9BWHE21JX24060960' })
  originalChassi: string;

  @ApiProperty({ example: '12345678901' })
  originalRenavam: string;

  @ApiProperty({ example: 'PR' })
  originalEstado: string;
}

export class PreSaleImportHistoryResponseDto {
  @ApiProperty({ example: '123e4567-e89b-12d3-a456-426614174000' })
  id: string;

  @ApiProperty({ example: 'vehicles.xlsx' })
  originalFileName: string;

  @ApiProperty({ example: '/uploads/vehicles.xlsx' })
  filePath: string;

  @ApiProperty({ enum: PreSaleImportHistoryStatus })
  status: PreSaleImportHistoryStatus;

  @ApiProperty({ example: 100 })
  totalRecords: number;

  @ApiProperty({ example: 80 })
  processedRecords: number;

  @ApiProperty({ example: 80 })
  successCount: number;

  @ApiProperty({ example: 15 })
  errorCount: number;

  @ApiProperty({ example: 5 })
  alreadyRegisteredCount: number;

  @ApiProperty({ example: 80, description: 'Progresso da importação em porcentagem' })
  progress: number;

  @ApiProperty({ type: [PreSaleImportHistoryErrorDto], required: false })
  errors?: PreSaleImportHistoryErrorDto[];
}
