import { ApiProperty } from '@nestjs/swagger';

export class ImportSummaryDto {
  @ApiProperty({
    description: 'ID da importação',
    example: '123e4567-e89b-12d3-a456-426614174000',
  })
  id: string;

  @ApiProperty({
    description: 'Nome do arquivo original',
    example: 'veiculos.csv',
  })
  originalFileName: string;

  @ApiProperty({
    description: 'Total de registros processados',
    example: 100,
  })
  totalProcessed: number;

  @ApiProperty({
    description: 'Total de registros importados com sucesso',
    example: 95,
  })
  totalSuccess: number;

  @ApiProperty({
    description: 'Total de registros com erro',
    example: 5,
  })
  totalErrors: number;

  @ApiProperty({
    description: 'Data da importação',
    example: '2024-03-20T10:30:00Z',
  })
  importedAt: Date;
}
