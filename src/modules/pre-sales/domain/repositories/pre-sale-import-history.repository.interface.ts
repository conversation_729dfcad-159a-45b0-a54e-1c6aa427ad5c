import { Paginated, PaginateQuery } from 'nestjs-paginate';
import { PreSaleImportHistoryEntity } from '../../infrastructure/entities/pre-sale-import-history.entity';
import { PreSaleImportHistoryVehicleEntity } from '../../infrastructure/entities/pre-sale-import-history-vehicle.entity';
import { PreSaleImportHistory } from '../entities/pre-sale-import-history';
import { PreSaleImportHistoryVehicle } from '../entities/pre-sale-import-history-vehicle';

export interface IPreSaleImportHistoryRepository {
  create(importHistory: PreSaleImportHistory): Promise<PreSaleImportHistory>;

  findById(id: string): Promise<PreSaleImportHistory>;

  update(id: string, data: Partial<PreSaleImportHistory>): Promise<void>;

  createVehicle(vehicle: PreSaleImportHistoryVehicle): Promise<PreSaleImportHistoryVehicle>;

  findVehiclesByImportHistoryId(importHistoryId: string, query: PaginateQuery): Promise<Paginated<PreSaleImportHistoryVehicleEntity>>;

  findAll(query: PaginateQuery): Promise<Paginated<PreSaleImportHistoryEntity>>;

  findImportHistoryVehicleByPlateChassiRenavam(
    importHistoryId: string,
    plate: string,
    chassi: string,
    renavam: string,
  ): Promise<PreSaleImportHistoryVehicleEntity>;

  updateImportHistoryVehicleStatus(importHistoryVehicleId: string, status: string): Promise<void>;
}
