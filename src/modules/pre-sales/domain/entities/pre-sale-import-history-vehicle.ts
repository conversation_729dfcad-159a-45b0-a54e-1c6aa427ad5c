export enum PreSaleImportHistoryVehicleStatus {
  STARTED = 'STARTED',
  PROCESSING = 'PROCESSING',
  SUCCESS = 'SUCCESS',
  ERROR = 'ERROR',
}

export class PreSaleImportHistoryVehicle {
  id?: string;
  importHistoryId: string;
  vehicleId?: string;
  lineNumber: number;
  status: PreSaleImportHistoryVehicleStatus;
  errorMessage?: string;

  constructor(importHistoryId: string, lineNumber: number) {
    this.importHistoryId = importHistoryId;
    this.lineNumber = lineNumber;
    this.status = PreSaleImportHistoryVehicleStatus.STARTED;
  }

  markAsStarted(vehicleId: string): void {
    this.vehicleId = vehicleId;
    this.status = PreSaleImportHistoryVehicleStatus.STARTED;
  }

  markAsError(errorMessage: string): void {
    this.status = PreSaleImportHistoryVehicleStatus.ERROR;
    this.errorMessage = errorMessage;
  }
}
