import { PreSaleImportHistoryVehicle } from './pre-sale-import-history-vehicle';

export enum PreSaleImportHistoryStatus {
  STARTED = 'STARTED',
  PROCESSING = 'PROCESSING',
  COMPLETED = 'COMPLETED',
  ERROR = 'ERROR',
}

export class PreSaleImportHistory {
  id: string;
  originalFileName: string;
  filePath: string;
  status: PreSaleImportHistoryStatus;
  totalRecords: number;
  processedRecords: number;
  successCount: number;
  errorCount: number;
  alreadyRegisteredCount: number;
  vehicles?: PreSaleImportHistoryVehicle[];

  constructor(originalFileName: string, filePath: string) {
    this.originalFileName = originalFileName;
    this.filePath = filePath;
    this.status = PreSaleImportHistoryStatus.STARTED;
    this.totalRecords = 0;
    this.processedRecords = 0;
    this.successCount = 0;
    this.errorCount = 0;
    this.alreadyRegisteredCount = 0;
  }

  get progress(): number {
    return this.totalRecords > 0 ? (this.processedRecords / this.totalRecords) * 100 : 0;
  }

  startProcessing(): void {
    if (this.status !== PreSaleImportHistoryStatus.STARTED) {
      throw new Error('Só é possível iniciar o processamento de uma importação pendente');
    }
    this.status = PreSaleImportHistoryStatus.PROCESSING;
  }

  completeProcessing(): void {
    if (this.status !== PreSaleImportHistoryStatus.PROCESSING) {
      throw new Error('Só é possível completar uma importação em processamento');
    }
    this.status = PreSaleImportHistoryStatus.COMPLETED;
  }

  markAsError(): void {
    this.status = PreSaleImportHistoryStatus.ERROR;
  }

  incrementSuccess(): void {
    this.successCount++;
    this.processedRecords++;
  }

  incrementError(): void {
    this.errorCount++;
    this.processedRecords++;
  }

  incrementAlreadyRegistered(): void {
    this.alreadyRegisteredCount++;
    this.processedRecords++;
  }
}
