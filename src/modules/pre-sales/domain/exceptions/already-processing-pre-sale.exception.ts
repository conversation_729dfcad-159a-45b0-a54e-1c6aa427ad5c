import { BusinessException } from '@/core/exceptions/business-exception';

export class AlreadyProcessingPreSaleException extends BusinessException {
  constructor(currentProcessingHistoryId: string) {
    const message = `O lote id ${currentProcessingHistoryId} está sendo processado no momento, aguarde o processamento finalizar.`;
    super(message);
    this.name = 'AlreadyProcessingPreSaleException';
  }
}
