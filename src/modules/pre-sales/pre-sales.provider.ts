import { CreateImportHistoryUseCase } from './application/use-cases/create-import-history.use-case';
import { FindAllImportHistoriesUseCase } from './application/use-cases/find-all-import-histories.use-case';
import { FindImportHistoryByIdUseCase } from './application/use-cases/find-import-history-by-id.use-case';
import { FindVehiclesByImportHistoryIdUseCase } from './application/use-cases/find-vehicles-by-import-history-id.use-case';
import { ProcessImportFileUseCase } from './application/use-cases/process-import-file.use-case';
import { PreSaleImportHistoryRepository } from './infrastructure/pre-sale-import-history.repository';
import { FindImportHistoryVehicleByPlateChassisRenavamUseCase } from './application/use-cases/find-import-history-vehicle-by-plate-chassis-renavam.use-case';
import { UpdateImportHistoryVehicleStatusUseCase } from './application/use-cases/update-import-history-vehicle-status.use-case';
import { ProcessResultImportFileUseCase } from './application/use-cases/process-result-import-file.use-case';
import { UpdateImportHistoryStatusUseCase } from './application/use-cases/update-import-history-status.use-case';
import { PreSaleImportHistoryResultRepository } from './infrastructure/repositories/pre-sale-import-history-result.repository';
import { ProcessImportHistoryResultVehicleUseCase } from './application/use-cases/process-import-history-result-vehicle.use-case';

export const PRE_SALES_PROVIDERS = [
  // Use Cases
  ProcessImportFileUseCase,
  CreateImportHistoryUseCase,
  FindAllImportHistoriesUseCase,
  FindImportHistoryByIdUseCase,
  FindVehiclesByImportHistoryIdUseCase,
  ProcessResultImportFileUseCase,
  UpdateImportHistoryStatusUseCase,
  FindImportHistoryVehicleByPlateChassisRenavamUseCase,
  UpdateImportHistoryVehicleStatusUseCase,
  ProcessImportHistoryResultVehicleUseCase,
  // Repositories
  {
    provide: 'IPreSaleImportHistoryRepository',
    useClass: PreSaleImportHistoryRepository,
  },
  {
    provide: 'IPreSaleImportHistoryResultRepository',
    useClass: PreSaleImportHistoryResultRepository,
  },
];
