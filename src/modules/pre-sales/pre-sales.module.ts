import { Module } from '@nestjs/common';
import { MulterModule } from '@nestjs/platform-express';
import { TypeOrmModule } from '@nestjs/typeorm';
import { memoryStorage } from 'multer';
import { VehiclesModule } from '../vehicles/vehicles.module';
import { PreSaleImportHistoryEntity } from './infrastructure/entities/pre-sale-import-history.entity';
import { PreSaleImportHistoryVehicleEntity } from './infrastructure/entities/pre-sale-import-history-vehicle.entity';
import { PRE_SALES_PROVIDERS } from './pre-sales.provider';
import { PreSalesController } from './presentation/controllers/pre-sales.controller';
import { VehicleFinesModule } from '../vehicle-fines/vehicle-fines.module';
import { VehicleDebitsModule } from '../vehicle-debits/vehicle-debits.module';
import { VehicleRecallsModule } from '../vehicle-recalls/vehicle-recalls.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([PreSaleImportHistoryEntity, PreSaleImportHistoryVehicleEntity]),
    MulterModule.register({
      storage: memoryStorage(),
      fileFilter: (req, file, cb) => {
        if (!file.originalname.match(/\.(xlsx|xls)$/)) {
          return cb(new Error('Apenas arquivos Excel são permitidos!'), false);
        }
        cb(null, true);
      },
      limits: {
        fileSize: 5 * 1024 * 1024, // 5MB
      },
    }),
    VehiclesModule,
    VehicleFinesModule,
    VehicleDebitsModule,
    VehicleRecallsModule,
  ],
  controllers: [PreSalesController],
  providers: [...PRE_SALES_PROVIDERS],
})
export class PreSalesModule {}
