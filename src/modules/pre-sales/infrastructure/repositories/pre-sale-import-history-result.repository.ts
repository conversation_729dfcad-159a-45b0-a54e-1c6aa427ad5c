import { Injectable } from '@nestjs/common';
import { DataSource } from 'typeorm';
import { VehicleEntity } from '@/modules/vehicles/infrastructure/vehicle.entity';
import { VehicleFineEntity } from '@/modules/vehicle-fines/infrastructure/entities/vehicle-fine.entity';
import { VehicleDebitEntity } from '@/modules/vehicle-debits/infrastructure/entities/vehicle-debit.entity';
import { IPreSaleImportHistoryResultRepository } from '../../domain/repositories/pre-sale-import-history-result.repository.interface';
import { Vehicle } from '@/modules/vehicles/domain/vehicle.entity';
import { VehicleDebit } from '@/modules/vehicle-debits/domain/entities/vehicle-debit.entity';
import { VehicleFine } from '@/modules/vehicle-fines/domain/entities/vehicle-fine.entity';
import { VehicleRecall } from '@/modules/vehicle-recalls/domain/entities/vehicle-recall.entity';
import { VehicleRecallEntity } from '@/modules/vehicle-recalls/infrastructure/entities/vehicle-recall.entity';
import { PreSaleImportHistoryVehicleEntity } from '../entities/pre-sale-import-history-vehicle.entity';
import { PreSaleImportHistoryVehicleStatus } from '../../domain/entities/pre-sale-import-history-vehicle';

@Injectable()
export class PreSaleImportHistoryResultRepository implements IPreSaleImportHistoryResultRepository {
  private static vehicleToEntity(vehicle: Vehicle): Partial<VehicleEntity> {
    return {
      id: vehicle.getId(),
      numberPlate: vehicle.getLicensePlate(),
      chassi: vehicle.getChassis(),
      renavam: vehicle.getRenavam(),
      state: vehicle.getState(),
      city: vehicle.getCity(),
      category: vehicle.getCategory(),
      situation: vehicle.getSituation(),
      gravameStatus: vehicle.getGravameStatus(),
      gravameSituation: vehicle.getGravameSituation(),
      owner: vehicle.getOwner(),
      ownerDocument: vehicle.getOwnerDocument(),
      lastCsvReport: vehicle.getLastCsvReport(),
      regularizationDeadline: vehicle.getRegularizationDeadline(),
      lastLicensing: vehicle.getLastLicensing(),
    };
  }

  private static vehicleFineToEntity(vehicleFine: VehicleFine): Partial<VehicleFineEntity> {
    return {
      vehicleId: vehicleFine.vehicleId,
      preSaleImportHistoryVehicleId: vehicleFine.preSaleImportHistoryVehicleId,
      fineCode: vehicleFine.fineCode,
      status: vehicleFine.status,
      date: vehicleFine.date,
      time: vehicleFine.time,
      enforcingAuthority: vehicleFine.enforcingAuthority,
      infractionCode: vehicleFine.infractionCode,
      description: vehicleFine.description,
      location: vehicleFine.location,
      value: vehicleFine.value,
      dueDate: vehicleFine.dueDate,
      barCode: vehicleFine.barCode,
    };
  }

  private static vehicleDebitToEntity(debit: VehicleDebit): Partial<VehicleDebitEntity> {
    return {
      vehicleId: debit.vehicleId,
      description: debit.description,
      value: debit.value,
      dueDate: debit.dueDate,
      status: debit.status,
      type: debit.type,
      preSaleImportHistoryVehicleId: debit.preSaleImportHistoryVehicleId,
    };
  }

  private vehicleRecallToEntity(recall: VehicleRecall): Partial<VehicleRecallEntity> {
    return {
      vehicleId: recall.vehicleId,
      preSaleImportHistoryVehicleId: recall.preSaleImportHistoryVehicleId,
      recall: recall.recall,
      description: recall.description,
      status: recall.status,
      registrationDate: recall.registrationDate,
      deadline: recall.deadline,
    };
  }

  constructor(private readonly dataSource: DataSource) {}

  async processVehicle(vehicle: Vehicle, preSaleImportHistoryVehicleId: string): Promise<void> {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      const vehicleRepository = queryRunner.manager.getRepository(VehicleEntity);
      const vehicleFineRepository = queryRunner.manager.getRepository(VehicleFineEntity);
      const vehicleRecallsRepository = queryRunner.manager.getRepository(VehicleRecallEntity);
      const vehicleDebitRepository = queryRunner.manager.getRepository(VehicleDebitEntity);
      const preSaleImportHistoryVehicleRepository = queryRunner.manager.getRepository(PreSaleImportHistoryVehicleEntity);

      const vehicleEntity = PreSaleImportHistoryResultRepository.vehicleToEntity(vehicle);
      const debitsToCreate = vehicle.getDebits().map((debit) => PreSaleImportHistoryResultRepository.vehicleDebitToEntity(debit));
      const recallsToCreate = vehicle.getRecalls().map((recall) => this.vehicleRecallToEntity(recall));
      const finesToCreate = vehicle.getFines().map((fine) => PreSaleImportHistoryResultRepository.vehicleFineToEntity(fine));

      await vehicleRepository.update({ id: vehicle.getId() }, vehicleEntity);
      await preSaleImportHistoryVehicleRepository.update({ id: preSaleImportHistoryVehicleId }, { status: PreSaleImportHistoryVehicleStatus.SUCCESS });
      const createdFines = vehicleFineRepository.create(finesToCreate);
      const createdRecalls = vehicleRecallsRepository.create(recallsToCreate);
      const createdDebits = vehicleDebitRepository.create(debitsToCreate);

      await vehicleFineRepository.save(createdFines);
      await vehicleRecallsRepository.save(createdRecalls);
      await vehicleDebitRepository.save(createdDebits);

      await queryRunner.commitTransaction();
    } catch (e) {
      await queryRunner.rollbackTransaction();
      throw e;
    } finally {
      await queryRunner.release();
    }
  }
}
