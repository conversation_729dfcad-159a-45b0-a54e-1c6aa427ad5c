import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';

import { paginate, Paginated, PaginateQuery } from 'nestjs-paginate';
import { PreSaleImportHistory } from '../domain/entities/pre-sale-import-history';
import { PreSaleImportHistoryVehicle, PreSaleImportHistoryVehicleStatus } from '../domain/entities/pre-sale-import-history-vehicle';
import { IPreSaleImportHistoryRepository } from '../domain/repositories/pre-sale-import-history.repository.interface';
import { PreSaleImportHistoryEntity } from './entities/pre-sale-import-history.entity';
import { PreSaleImportHistoryVehicleEntity } from './entities/pre-sale-import-history-vehicle.entity';
import { PRE_SALE_IMPORT_HISTORY_PAGINATE_CONFIG, PRE_SALE_IMPORT_HISTORY_VEHICLE_PAGINATE_CONFIG } from './pre-sale.query';

@Injectable()
export class PreSaleImportHistoryRepository implements IPreSaleImportHistoryRepository {
  constructor(
    @InjectRepository(PreSaleImportHistoryEntity)
    private readonly importHistoryRepository: Repository<PreSaleImportHistoryEntity>,
    @InjectRepository(PreSaleImportHistoryVehicleEntity)
    private readonly importHistoryVehicleRepository: Repository<PreSaleImportHistoryVehicleEntity>,
  ) {}

  async create(importHistory: PreSaleImportHistory): Promise<PreSaleImportHistory> {
    const entity = this.importHistoryRepository.create({
      fileName: importHistory.originalFileName,
      originalFilePath: importHistory.filePath,
      status: importHistory.status,
      totalRecords: importHistory.totalRecords,
      totalProcessed: importHistory.processedRecords,
      successCount: importHistory.successCount,
      errorCount: importHistory.errorCount,
      alreadyRegisteredCount: importHistory.alreadyRegisteredCount,
    });

    const savedEntity = await this.importHistoryRepository.save(entity);

    const result = new PreSaleImportHistory(savedEntity.fileName, savedEntity.originalFilePath);
    result.id = savedEntity.id;
    result.status = savedEntity.status;
    result.totalRecords = savedEntity.totalRecords;
    result.processedRecords = savedEntity.totalProcessed;
    result.successCount = savedEntity.successCount;
    result.errorCount = savedEntity.errorCount;
    result.alreadyRegisteredCount = savedEntity.alreadyRegisteredCount;

    return result;
  }

  async findById(id: string): Promise<PreSaleImportHistory> {
    const entity = await this.importHistoryRepository.findOne({
      where: { id },
    });

    if (!entity) {
      return null;
    }

    const result = new PreSaleImportHistory(entity.fileName, entity.originalFilePath);
    result.id = entity.id;
    result.status = entity.status;
    result.totalRecords = entity.totalRecords;
    result.processedRecords = entity.totalProcessed;
    result.successCount = entity.successCount;
    result.errorCount = entity.errorCount;
    result.alreadyRegisteredCount = entity.alreadyRegisteredCount;

    const vehicles = await this.importHistoryVehicleRepository.find({
      where: { importHistoryId: id },
    });

    result.vehicles = vehicles.map((vehicle) => {
      const vehicleResult = new PreSaleImportHistoryVehicle(vehicle.importHistoryId, vehicle.lineNumber);
      vehicleResult.id = vehicle.id;
      vehicleResult.vehicleId = vehicle.vehicleId;
      vehicleResult.status = vehicle.status;
      vehicleResult.errorMessage = vehicle.errorMessage;
      return vehicleResult;
    });

    return result;
  }

  async update(id: string, data: Partial<PreSaleImportHistory>): Promise<void> {
    await this.importHistoryRepository.update(id, {
      fileName: data.originalFileName,
      originalFilePath: data.filePath,
      status: data.status,
      totalRecords: data.totalRecords,
      totalProcessed: data.processedRecords,
      successCount: data.successCount,
      errorCount: data.errorCount,
      alreadyRegisteredCount: data.alreadyRegisteredCount,
    });
  }

  async createVehicle(vehicle: PreSaleImportHistoryVehicle): Promise<PreSaleImportHistoryVehicle> {
    const entity = this.importHistoryVehicleRepository.create({
      importHistoryId: vehicle.importHistoryId,
      vehicleId: vehicle.vehicleId,
      lineNumber: vehicle.lineNumber,
      status: vehicle.status,
      errorMessage: vehicle.errorMessage,
    });

    const savedEntity = await this.importHistoryVehicleRepository.save(entity);

    const result = new PreSaleImportHistoryVehicle(savedEntity.importHistoryId, savedEntity.lineNumber);
    result.id = savedEntity.id;
    result.vehicleId = savedEntity.vehicleId;
    result.status = savedEntity.status;
    result.errorMessage = savedEntity.errorMessage;

    return result;
  }

  async findVehiclesByImportHistoryId(importHistoryId: string, query: PaginateQuery): Promise<Paginated<PreSaleImportHistoryVehicleEntity>> {
    return paginate(query, this.importHistoryVehicleRepository, {
      ...PRE_SALE_IMPORT_HISTORY_VEHICLE_PAGINATE_CONFIG,
      where: { importHistoryId },
    });
  }

  async findAll(query: PaginateQuery): Promise<Paginated<PreSaleImportHistoryEntity>> {
    return await paginate(query, this.importHistoryRepository, PRE_SALE_IMPORT_HISTORY_PAGINATE_CONFIG);
  }

  findImportHistoryVehicleByPlateChassiRenavam(importHistoryId: string, plate: string, chassi: string, renavam: string) {
    const queryBuilder = this.importHistoryVehicleRepository
      .createQueryBuilder('importHistoryVehicle')
      .leftJoinAndSelect(`importHistoryVehicle.vehicle`, 'vehicle')
      .where('importHistoryVehicle.import_history_id = :importHistoryId', { importHistoryId });

    if (plate) {
      queryBuilder.andWhere(`vehicle.number_plate = :plate`, { plate });
    }

    if (chassi) {
      queryBuilder.andWhere(`vehicle.chassi = :chassi`, { chassi });
    }

    if (renavam) {
      // queryBuilder.andWhere(`vehicle.renavam = :renavam`, { renavam });
    }

    const [sql, params] = queryBuilder.getQueryAndParameters();
    console.log(sql);
    console.log(params);

    return queryBuilder.getOne();
  }

  async updateImportHistoryVehicleStatus(importHistoryVehicleId: string, status: PreSaleImportHistoryVehicleStatus): Promise<void> {
    await this.importHistoryVehicleRepository.update({ id: importHistoryVehicleId }, { status });
  }
}
