import { BaseEntity } from '@/core/infrastructure/common/abstracts/entity.abstract';
import { Column, Entity, Index, OneToMany, PrimaryGeneratedColumn } from 'typeorm';
import { PreSaleImportHistoryVehicleEntity } from './pre-sale-import-history-vehicle.entity';
import { PreSaleImportHistoryStatus } from '../../domain/entities/pre-sale-import-history';

@Entity('pre_sale_import_history')
export class PreSaleImportHistoryEntity extends BaseEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Index()
  @Column({ name: 'file_name' })
  fileName: string;

  @Column({ name: 'original_file_path' })
  originalFilePath: string;

  @Index()
  @Column({
    type: 'enum',
    enum: PreSaleImportHistoryStatus,
    default: PreSaleImportHistoryStatus.STARTED,
  })
  status: PreSaleImportHistoryStatus;

  @Column({ name: 'total_records', default: 0 })
  totalRecords: number;

  @Column({ name: 'processed_records', default: 0 })
  totalProcessed: number;

  @Column({ name: 'success_count', default: 0 })
  successCount: number;

  @Column({ name: 'error_count', default: 0 })
  errorCount: number;

  @Column({ name: 'already_registered_count', default: 0 })
  alreadyRegisteredCount: number;

  @Column({ name: 'report_url', nullable: true })
  reportUrl?: string;

  @OneToMany(() => PreSaleImportHistoryVehicleEntity, (vehiclesHistory) => vehiclesHistory.importHistory, {
    cascade: ['insert', 'update'],
    onDelete: 'CASCADE',
  })
  vehiclesHistory: PreSaleImportHistoryVehicleEntity[];
}
