import { BaseEntity } from '@/core/infrastructure/common/abstracts/entity.abstract';
import { VehicleEntity } from '@/modules/vehicles/infrastructure/vehicle.entity';
import { Column, Entity, Index, JoinColumn, ManyToOne, PrimaryGeneratedColumn } from 'typeorm';

import { PreSaleImportHistoryVehicleStatus } from '../../domain/entities/pre-sale-import-history-vehicle';
import { PreSaleImportHistoryEntity } from './pre-sale-import-history.entity';

@Entity('pre_sale_import_history_vehicles')
export class PreSaleImportHistoryVehicleEntity extends BaseEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Index()
  @Column({ name: 'import_history_id' })
  importHistoryId: string;

  @Index()
  @Column({ name: 'vehicle_id', nullable: true })
  vehicleId?: string;

  @Column({ name: 'line_number' })
  lineNumber: number;

  @Column({ type: 'enum', enum: PreSaleImportHistoryVehicleStatus })
  status: PreSaleImportHistoryVehicleStatus;

  @Column({ name: 'error_message', nullable: true })
  errorMessage?: string;

  @ManyToOne(() => PreSaleImportHistoryEntity)
  @JoinColumn({ name: 'import_history_id' })
  importHistory: PreSaleImportHistoryEntity;

  @ManyToOne(() => VehicleEntity)
  @JoinColumn({ name: 'vehicle_id' })
  vehicle?: VehicleEntity;
}
