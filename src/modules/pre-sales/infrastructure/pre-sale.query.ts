import { FilterOperator, PaginateConfig } from 'nestjs-paginate';
import { PreSaleImportHistoryEntity } from './entities/pre-sale-import-history.entity';
import { PreSaleImportHistoryVehicleEntity } from './entities/pre-sale-import-history-vehicle.entity';

export const PRE_SALE_IMPORT_HISTORY_PAGINATE_CONFIG: PaginateConfig<PreSaleImportHistoryEntity> = {
  defaultSortBy: [['createdAt', 'DESC']],
  sortableColumns: ['createdAt', 'fileName', 'status', 'totalRecords', 'totalProcessed', 'successCount', 'errorCount', 'alreadyRegisteredCount'],
  searchableColumns: ['createdAt', 'fileName', 'status', 'totalRecords', 'totalProcessed', 'successCount', 'errorCount', 'alreadyRegisteredCount'],
  filterableColumns: {
    createdAt: [FilterOperator.ILIKE],
    fileName: [FilterOperator.ILIKE],
    status: [FilterOperator.ILIKE],
    totalRecords: [FilterOperator.ILIKE],
    totalProcessed: [FilterOperator.ILIKE],
    successCount: [FilterOperator.ILIKE],
    errorCount: [FilterOperator.ILIKE],
    alreadyRegisteredCount: [FilterOperator.ILIKE],
  },
};

export const PRE_SALE_IMPORT_HISTORY_VEHICLE_PAGINATE_CONFIG: PaginateConfig<PreSaleImportHistoryVehicleEntity> = {
  select: [
    'id',
    'lineNumber',
    'status',
    'errorMessage',
    'createdAt',
    'vehicle.id',
    'vehicle.numberPlate',
    'vehicle.chassi',
    'vehicle.renavam',
    'vehicle.state',
  ],
  sortableColumns: ['createdAt', 'status'],
  searchableColumns: ['createdAt', 'status'],
  filterableColumns: {
    createdAt: [FilterOperator.ILIKE],
    status: [FilterOperator.ILIKE],
    errorMessage: [FilterOperator.ILIKE],
  },
  relations: ['vehicle'],
};
