import { Inject, Injectable } from '@nestjs/common';
import { VehicleDebit } from '../domain/entities/vehicle-debit.entity';
import { IVehicleDebitRepository } from '../domain/repositories/vehicle-debit.repository.interface';

type Input = {
  debit: VehicleDebit;
};

@Injectable()
export class CreateVehicleDebitUseCase {
  constructor(@Inject('IVehicleDebitRepository') private readonly vehicleDebitRepository: IVehicleDebitRepository) {}

  async execute({ debit }: Input) {
    return this.vehicleDebitRepository.create(debit);
  }
}
