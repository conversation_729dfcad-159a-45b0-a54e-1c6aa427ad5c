import { BaseEntity } from '@/core/infrastructure/common/abstracts/entity.abstract';
import { VehicleEntity } from '@/modules/vehicles/infrastructure/vehicle.entity';
import { Column, Entity, JoinColumn, ManyToOne, PrimaryGeneratedColumn } from 'typeorm';
import { PreSaleImportHistoryVehicleEntity } from '@/modules/pre-sales/infrastructure/entities/pre-sale-import-history-vehicle.entity';

@Entity('vehicle_debits')
export class VehicleDebitEntity extends BaseEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ name: 'vehicle_id' })
  vehicleId: string;

  @ManyToOne(() => VehicleEntity)
  @JoinColumn({ name: 'vehicle_id' })
  vehicle: VehicleEntity;

  @Column({ name: 'pre_sale_import_history_vehicle_id', nullable: true })
  preSaleImportHistoryVehicleId?: string;

  @ManyToOne(() => PreSaleImportHistoryVehicleEntity, { nullable: true })
  @JoinColumn({ name: 'pre_sale_import_history_vehicle_id' })
  preSaleImportHistoryVehicle?: PreSaleImportHistoryVehicleEntity;

  @Column({ length: 255 })
  description: string;

  @Column({ type: 'decimal', precision: 10, scale: 2 })
  value: number;

  @Column({ name: 'due_date', nullable: true })
  dueDate: Date;

  @Column({ length: 20 })
  status: string;

  @Column({ length: 20 })
  type: string;
}
