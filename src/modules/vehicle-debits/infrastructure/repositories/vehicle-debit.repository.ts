import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { VehicleDebit } from '../../domain/entities/vehicle-debit.entity';
import { IVehicleDebitRepository } from '../../domain/repositories/vehicle-debit.repository.interface';

import { VehicleDebitEntity } from '../entities/vehicle-debit.entity';

@Injectable()
export class VehicleDebitRepository implements IVehicleDebitRepository {
  constructor(
    @InjectRepository(VehicleDebitEntity)
    private readonly repository: Repository<VehicleDebitEntity>,
  ) {}

  private toDomain(entity: VehicleDebitEntity): VehicleDebit {
    return new VehicleDebit(entity.vehicleId, entity.description, entity.value, entity.dueDate, entity.type, entity.id);
  }

  private toEntity(domain: VehicleDebit): Partial<VehicleDebitEntity> {
    return {
      id: domain.id,
      vehicleId: domain.vehicleId,
      description: domain.description,
      value: domain.value,
      dueDate: domain.dueDate,
      status: domain.status,
      type: domain.type,
    };
  }

  async create(debit: VehicleDebit): Promise<VehicleDebit> {
    const entity = this.repository.create(this.toEntity(debit));
    const savedEntity = await this.repository.save(entity);
    return this.toDomain(savedEntity);
  }

  async findOneById(id: string): Promise<VehicleDebit> {
    const entity = await this.repository.findOneByOrFail({ id });
    return this.toDomain(entity);
  }

  async findPendingByVehicleId(vehicleId: string): Promise<VehicleDebit[]> {
    const entities = await this.repository.find({
      where: {
        vehicleId,
        status: 'PENDING',
      },
    });
    return entities.map((entity) => this.toDomain(entity));
  }

  async update(id: string, data: Partial<VehicleDebit>): Promise<void> {
    await this.repository.update({ id }, this.toEntity(data as VehicleDebit));
  }

  async delete(id: string): Promise<void> {
    await this.repository.softDelete(id);
  }
}
