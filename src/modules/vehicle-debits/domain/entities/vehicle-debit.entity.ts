import { BadRequestException } from '@nestjs/common';

export class VehicleDebit {
  private _id: string;
  private _vehicleId: string;
  private _description: string;
  private _value: number;
  private _dueDate: Date;
  private _status: string;
  private _preSaleImportHistoryVehicleId?: string;
  private readonly _type: string;

  constructor(vehicleId: string, description: string, value: number, dueDate: Date, type: string, id?: string, preSaleImportHistoryVehicleId?: string) {
    this.validateVehicleId(vehicleId);
    this.validateDescription(description);
    this.validateValue(value);
    this.validateDueDate(dueDate);
    this.validateType(type);

    this._id = id;
    this._vehicleId = vehicleId;
    this._description = description;
    this._value = value;
    this._dueDate = dueDate;
    this._type = type;
    this._status = 'PENDING'; // Status inicial
    this._preSaleImportHistoryVehicleId = preSaleImportHistoryVehicleId;
  }

  // Getters
  get id(): string {
    return this._id;
  }

  get vehicleId(): string {
    return this._vehicleId;
  }

  get description(): string {
    return this._description;
  }

  get value(): number {
    return this._value;
  }

  get dueDate(): Date {
    return this._dueDate;
  }

  get status(): string {
    return this._status;
  }

  get type(): string {
    return this._type;
  }

  get preSaleImportHistoryVehicleId(): string {
    return this._preSaleImportHistoryVehicleId;
  }

  // Validações
  private validateVehicleId(vehicleId: string): void {
    if (!vehicleId) {
      throw new BadRequestException('ID do veículo é obrigatório');
    }
  }

  private validateDescription(description: string): void {
    if (!description) {
      throw new BadRequestException('Descrição é obrigatória');
    }
    if (description.length > 255) {
      throw new BadRequestException('Descrição não pode ter mais que 255 caracteres');
    }
  }

  private validateValue(value: number): void {
    if (value <= 0) {
      throw new BadRequestException('Valor deve ser maior que zero');
    }
  }

  private validateDueDate(dueDate: Date): void {
    if (dueDate && dueDate < new Date(1900, 0, 1)) {
      throw new BadRequestException('Data de vencimento inválida');
    }
  }

  private validateType(type: string): void {
    const validTypes = ['IPVA', 'LICENCIAMENTO', 'DPVAT', 'OUTROS'];
    if (!validTypes.includes(type)) {
      throw new BadRequestException('Tipo de débito inválido');
    }
  }

  // Métodos de negócio
  markAsPaid(): void {
    if (this._status === 'PAID') {
      throw new BadRequestException('Débito já está pago');
    }
    this._status = 'PAID';
  }

  markAsCanceled(): void {
    if (this._status === 'CANCELED') {
      throw new BadRequestException('Débito já está cancelado');
    }
    if (this._status === 'PAID') {
      throw new BadRequestException('Não é possível cancelar um débito pago');
    }
    this._status = 'CANCELED';
  }

  updateDescription(description: string): void {
    this.validateDescription(description);
    this._description = description;
  }

  updateValue(value: number): void {
    this.validateValue(value);
    this._value = value;
  }

  updateDueDate(dueDate: Date): void {
    this.validateDueDate(dueDate);
    this._dueDate = dueDate;
  }

  toJSON() {
    return {
      id: this._id,
      vehicleId: this._vehicleId,
      description: this._description,
      value: this._value,
      dueDate: this._dueDate,
      status: this._status,
      type: this._type,
    };
  }
}
