import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { VehicleDebitEntity } from './infrastructure/entities/vehicle-debit.entity';
import { VEHICLE_DEBITS_EXPORTS, VEHICLE_DEBITS_PROVIDERS } from './vehicle-debits.providers';

@Module({
  imports: [TypeOrmModule.forFeature([VehicleDebitEntity])],
  providers: VEHICLE_DEBITS_PROVIDERS,
  exports: VEHICLE_DEBITS_EXPORTS,
})
export class VehicleDebitsModule {}
