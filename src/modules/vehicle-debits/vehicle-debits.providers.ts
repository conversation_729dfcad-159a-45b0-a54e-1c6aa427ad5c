import { Provider } from '@nestjs/common';
import { VehicleDebitRepository } from './infrastructure/repositories/vehicle-debit.repository';
import { CreateVehicleDebitUseCase } from './application/create-vehicle-debit.use-case';

const REPOSITORIES: Provider[] = [
  {
    provide: 'IVehicleDebitRepository',
    useClass: VehicleDebitRepository,
  },
];

export const VEHICLE_DEBITS_PROVIDERS = [...REPOSITORIES, CreateVehicleDebitUseCase];

export const VEHICLE_DEBITS_EXPORTS = ['IVehicleDebitRepository', CreateVehicleDebitUseCase];
