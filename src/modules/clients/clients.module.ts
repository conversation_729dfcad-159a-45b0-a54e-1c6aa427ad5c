import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { CLIENTS_EXPORTS, CLIENTS_PROVIDERS } from './clients.provider';
import { ClientEntity } from './infrastructure/client.entity';
import { ClientsController } from './presentation/clients.controller';

@Module({
  imports: [TypeOrmModule.forFeature([ClientEntity])],
  controllers: [ClientsController],
  providers: [...CLIENTS_PROVIDERS],
  exports: [...CLIENTS_EXPORTS],
})
export class ClientsModule {}
