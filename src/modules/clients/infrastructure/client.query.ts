import { PaginateConfig } from 'nestjs-paginate';
import { FilterOperator, FilterSuffix } from 'nestjs-paginate';
import { ClientEntity } from './client.entity';

export const CLIENT_PAGINATE_CONFIG: PaginateConfig<ClientEntity> = {
  sortableColumns: ['name', 'description', 'active', 'createdAt'],
  defaultSortBy: [['createdAt', 'DESC']],
  searchableColumns: ['name', 'description', 'active', 'createdAt'],
  filterableColumns: {
    active: [FilterOperator.EQ, FilterSuffix.NOT],
    name: [FilterOperator.ILIKE],
    description: [FilterOperator.ILIKE],
  },
};
