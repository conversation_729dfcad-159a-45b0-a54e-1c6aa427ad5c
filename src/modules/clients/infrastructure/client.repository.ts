import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { paginate, Paginated, PaginateQuery } from 'nestjs-paginate';
import { Repository } from 'typeorm';
import { IClientRepository } from '../domain/client.repository.interface';
import { ClientCreateDto } from '../presentation/client.dto';
import { ClientEntity } from './client.entity';
import { CLIENT_PAGINATE_CONFIG } from './client.query';
@Injectable()
export class ClientRepository implements IClientRepository {
  constructor(
    @InjectRepository(ClientEntity)
    private readonly repository: Repository<ClientEntity>,
  ) {}

  async create(client: ClientCreateDto): Promise<ClientEntity> {
    const entity = this.repository.create(client);
    return await this.repository.save(entity);
  }

  async findAll(query: PaginateQuery): Promise<Paginated<ClientEntity>> {
    return await paginate(query, this.repository, CLIENT_PAGINATE_CONFIG);
  }

  async findById(id: string): Promise<ClientEntity | null> {
    return await this.repository.findOneBy({ id });
  }

  async update(id: string, client: ClientCreateDto): Promise<void> {
    await this.repository.update(id, client);
  }

  async delete(id: string): Promise<void> {
    await this.repository.softDelete(id);
  }
}
