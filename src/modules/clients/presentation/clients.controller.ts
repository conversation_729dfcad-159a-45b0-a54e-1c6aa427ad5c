import { Body, Controller, Delete, Get, Param, Patch, Post } from '@nestjs/common';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { plainToInstance } from 'class-transformer';
import { Roles } from 'nest-keycloak-connect';
import { Paginate, Paginated, PaginatedSwaggerDocs, PaginateQuery } from 'nestjs-paginate';
import { CreateClientUseCase } from '../application/create-client.use-case';
import { DeleteClientUseCase } from '../application/delete-client.use-case';
import { FindAllClientsUseCase } from '../application/find-all-clients.use-case';
import { FindClientByIdUseCase } from '../application/find-client-by-id.use-case';
import { UpdateClientUseCase } from '../application/update-client.use-case';

import { CLIENT_PAGINATE_CONFIG } from '../infrastructure/client.query';
import { <PERSON><PERSON><PERSON>reateDto, ClientResponseDto } from './client.dto';
import { ClientEntity } from '../infrastructure/client.entity';

@ApiTags('Clientes')
@Controller('clients')
export class ClientsController {
  constructor(
    private readonly createClientUseCase: CreateClientUseCase,
    private readonly findAllClientsUseCase: FindAllClientsUseCase,
    private readonly findClientByIdUseCase: FindClientByIdUseCase,
    private readonly updateClientUseCase: UpdateClientUseCase,
    private readonly deleteClientUseCase: DeleteClientUseCase,
  ) {}

  @Post()
  @Roles({ roles: ['super-admin'] })
  @ApiOperation({ summary: 'Criar um novo cliente' })
  @ApiResponse({ status: 201, description: 'Cliente criado com sucesso', type: ClientResponseDto })
  async create(@Body() createClientDto: ClientCreateDto) {
    const client = await this.createClientUseCase.execute(createClientDto);
    return plainToInstance(ClientResponseDto, client);
  }

  @Get()
  @Roles({ roles: ['super-admin'] })
  @ApiOperation({ summary: 'Listar todos os clientes' })
  @PaginatedSwaggerDocs(ClientResponseDto, CLIENT_PAGINATE_CONFIG)
  async findAll(
    @Paginate()
    query: PaginateQuery,
  ) {
    const result: Paginated<ClientEntity> = await this.findAllClientsUseCase.execute(query);

    return {
      ...result,
      data: result.data.map((client) => plainToInstance(ClientResponseDto, client)),
    };
  }

  @Get(':id')
  @Roles({ roles: ['super-admin'] })
  @ApiOperation({ summary: 'Buscar um cliente pelo ID' })
  @ApiResponse({ status: 200, description: 'Cliente encontrado', type: ClientResponseDto })
  async findOne(@Param('id') id: string) {
    const client = await this.findClientByIdUseCase.execute(id);
    return plainToInstance(ClientResponseDto, client);
  }

  @Patch(':id')
  @Roles({ roles: ['super-admin'] })
  @ApiOperation({ summary: 'Atualizar um cliente' })
  @ApiResponse({ status: 200, description: 'Cliente atualizado', type: ClientResponseDto })
  async update(@Param('id') id: string, @Body() updateClientDto: Partial<ClientCreateDto>) {
    return await this.updateClientUseCase.execute(id, updateClientDto);
  }

  @Delete(':id')
  @Roles({ roles: ['super-admin'] })
  @ApiOperation({ summary: 'Remover um cliente' })
  @ApiResponse({ status: 204, description: 'Cliente removido' })
  async remove(@Param('id') id: string) {
    return await this.deleteClientUseCase.execute(id);
  }
}
