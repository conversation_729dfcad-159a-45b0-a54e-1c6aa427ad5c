import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsOptional, IsString, IsBoolean } from 'class-validator';
export class ClientCreateDto {
  @ApiProperty({ description: 'Nome do cliente', example: '<PERSON>' })
  @IsNotEmpty()
  @IsString()
  name: string;

  @ApiProperty({ description: 'Descrição do cliente', example: 'Cliente VIP', required: false })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({ description: 'Status ativo/inativo do cliente', example: true })
  @IsOptional()
  @IsBoolean({ message: 'O status deve ser um booleano' })
  active?: boolean;
}

export class ClientResponseDto extends ClientCreateDto {
  @ApiProperty({ description: 'ID do cliente', example: 'uuid-value' })
  id: string;
}
