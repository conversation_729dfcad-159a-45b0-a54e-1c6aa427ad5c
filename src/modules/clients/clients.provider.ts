import { CreateClientUseCase } from './application/create-client.use-case';
import { DeleteClientUseCase } from './application/delete-client.use-case';
import { FindAllClientsUseCase } from './application/find-all-clients.use-case';
import { FindClientByIdUseCase } from './application/find-client-by-id.use-case';
import { UpdateClientUseCase } from './application/update-client.use-case';
import { ClientRepository } from './infrastructure/client.repository';

const REPOSITORIES = [
  {
    provide: 'IClientRepository',
    useClass: ClientRepository,
  },
];

export const CLIENTS_PROVIDERS = [
  // UseCases
  CreateClientUseCase,
  FindAllClientsUseCase,
  FindClientByIdUseCase,
  UpdateClientUseCase,
  DeleteClientUseCase,
  // Repositories
  ...REPOSITORIES,
];

export const CLIENTS_EXPORTS = REPOSITORIES;
