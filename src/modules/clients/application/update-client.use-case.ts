import { Inject, Injectable, NotFoundException } from '@nestjs/common';
import { IClientRepository } from '../domain/client.repository.interface';
import { ClientCreateDto } from '../presentation/client.dto';

@Injectable()
export class UpdateClientUseCase {
  constructor(
    @Inject('IClientRepository')
    private readonly clientRepository: IClientRepository,
  ) {}

  async execute(id: string, updateClientDto: Partial<ClientCreateDto>): Promise<void> {
    const client = await this.clientRepository.findById(id);

    if (!client) {
      throw new NotFoundException(`Cliente com ID ${id} não encontrado`);
    }

    await this.clientRepository.update(id, updateClientDto);
  }
}
