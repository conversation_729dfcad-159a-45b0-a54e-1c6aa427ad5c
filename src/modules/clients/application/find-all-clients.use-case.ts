import { Inject, Injectable } from '@nestjs/common';
import { Paginated, PaginateQuery } from 'nestjs-paginate';
import { IClientRepository } from '../domain/client.repository.interface';
import { ClientEntity } from '../infrastructure/client.entity';

@Injectable()
export class FindAllClientsUseCase {
  constructor(
    @Inject('IClientRepository')
    private readonly clientRepository: IClientRepository,
  ) {}

  async execute(query: PaginateQuery): Promise<Paginated<ClientEntity>> {
    return this.clientRepository.findAll(query);
  }
}
