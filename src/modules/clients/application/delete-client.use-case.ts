import { Injectable, Inject, NotFoundException } from '@nestjs/common';
import { IClientRepository } from '../domain/client.repository.interface';

@Injectable()
export class DeleteClientUseCase {
  constructor(
    @Inject('IClientRepository')
    private readonly clientRepository: IClientRepository,
  ) {}

  async execute(id: string): Promise<void> {
    const client = await this.clientRepository.findById(id);
    if (!client) {
      throw new NotFoundException(`Cliente com ID ${id} não encontrado`);
    }
    await this.clientRepository.delete(id);
  }
}
