import { Inject, Injectable } from '@nestjs/common';

import { IClientRepository } from '../domain/client.repository.interface';
import { ClientEntity } from '../infrastructure/client.entity';
import { ClientCreateDto } from '../presentation/client.dto';

@Injectable()
export class CreateClientUseCase {
  constructor(
    @Inject('IClientRepository')
    private readonly clientRepository: IClientRepository,
  ) {}

  async execute(createClientDto: ClientCreateDto): Promise<ClientEntity> {
    return this.clientRepository.create(createClientDto);
  }
}
