import { Injectable, Inject, NotFoundException } from '@nestjs/common';
import { IClientRepository } from '../domain/client.repository.interface';
import { ClientEntity } from '../infrastructure/client.entity';

@Injectable()
export class FindClientByIdUseCase {
  constructor(
    @Inject('IClientRepository')
    private readonly clientRepository: IClientRepository,
  ) {}

  async execute(id: string): Promise<ClientEntity | null> {
    const client = await this.clientRepository.findById(id);
    if (!client) {
      throw new NotFoundException(`Cliente com ID ${id} não encontrado`);
    }
    return client;
  }
}
