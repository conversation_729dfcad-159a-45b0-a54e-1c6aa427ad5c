import { PaginateQuery, Paginated } from 'nestjs-paginate';
import { ClientEntity } from '../infrastructure/client.entity';
import { ClientCreateDto } from '../presentation/client.dto';

export interface IClientRepository {
  create(client: ClientCreateDto): Promise<ClientEntity>;
  findAll(query: PaginateQuery): Promise<Paginated<ClientEntity>>;
  findById(id: string): Promise<ClientEntity | null>;
  update(id: string, client: Partial<ClientCreateDto>): Promise<void>;
  delete(id: string): Promise<void>;
}
