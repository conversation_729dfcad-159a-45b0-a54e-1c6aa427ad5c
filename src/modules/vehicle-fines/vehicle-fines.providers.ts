import { Provider } from '@nestjs/common';
import { VehicleFinesRepository } from './infrastructure/repositories/vehicle-fines.repository';
import { CreateVehicleFineUseCase } from './domain/application/create-vehicle-fine-use-case.service';

const REPOSITORIES: Provider[] = [{ provide: 'IVehicleFinesRepository', useClass: VehicleFinesRepository }];

export const VEHICLE_FINES_PROVIDERS = [...REPOSITORIES, CreateVehicleFineUseCase];

export const VEHICLE_FINES_EXPORTS = ['IVehicleFinesRepository', CreateVehicleFineUseCase];
