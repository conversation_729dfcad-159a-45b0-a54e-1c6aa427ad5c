import { Module } from '@nestjs/common';
import { VEHICLE_FINES_EXPORTS, VEHICLE_FINES_PROVIDERS } from './vehicle-fines.providers';
import { TypeOrmModule } from '@nestjs/typeorm';
import { VehicleFineEntity } from './infrastructure/entities/vehicle-fine.entity';

@Module({
  imports: [TypeOrmModule.forFeature([VehicleFineEntity])],
  providers: VEHICLE_FINES_PROVIDERS,
  exports: VEHICLE_FINES_EXPORTS,
})
export class VehicleFinesModule {}
