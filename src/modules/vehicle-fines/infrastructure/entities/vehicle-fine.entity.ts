import { <PERSON>um<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne } from 'typeorm';
import { BaseEntity } from '@/core/infrastructure/common/abstracts/entity.abstract';
import { VehicleEntity } from '@/modules/vehicles/infrastructure/vehicle.entity';
import { PreSaleImportHistoryVehicleEntity } from '@/modules/pre-sales/infrastructure/entities/pre-sale-import-history-vehicle.entity';

@Entity('vehicle_fines')
export class VehicleFineEntity extends BaseEntity {
  @Column({ name: 'vehicle_id' })
  vehicleId: string;

  @ManyToOne(() => VehicleEntity)
  @JoinColumn({ name: 'vehicle_id' })
  vehicle: VehicleEntity;

  @Column({ name: 'pre_sale_import_history_vehicle_id', nullable: true })
  preSaleImportHistoryVehicleId?: string;

  @ManyToOne(() => PreSaleImportHistoryVehicleEntity, { nullable: true })
  @JoinColumn({ name: 'pre_sale_import_history_vehicle_id' })
  preSaleImportHistoryVehicle?: PreSaleImportHistoryVehicleEntity;

  @Column({ name: 'fine_code' })
  fineCode: string;

  @Column({ length: 20 })
  status: string;

  @Column({ type: 'date' })
  date: Date;

  @Column({ type: 'time' })
  time: string;

  @Column({ name: 'enforcing_authority', length: 100 })
  enforcingAuthority: string;

  @Column({ name: 'infraction_code', length: 10 })
  infractionCode: string;

  @Column({ length: 255 })
  description: string;

  @Column({ length: 255 })
  location: string;

  @Column({ type: 'decimal', precision: 10, scale: 2 })
  value: number;

  @Column({ name: 'due_date', nullable: true })
  dueDate?: Date;

  @Column({ name: 'bar_code', length: 48, nullable: true })
  barCode?: string;
}
