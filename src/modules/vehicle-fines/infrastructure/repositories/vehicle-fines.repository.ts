import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { IVehicleFinesRepository } from '../../domain/repositories/vehicle-fine.repository.interface';
import { VehicleFine } from '../../domain/entities/vehicle-fine.entity';
import { VehicleFineEntity } from '../entities/vehicle-fine.entity';

@Injectable()
export class VehicleFinesRepository implements IVehicleFinesRepository {
  constructor(@InjectRepository(VehicleFineEntity) private readonly repository: Repository<VehicleFineEntity>) {}

  private toDomain(entity: VehicleFineEntity): VehicleFine {
    return VehicleFine.create({
      id: entity.id,
      vehicleId: entity.vehicleId,
      preSaleImportHistoryVehicleId: entity.preSaleImportHistoryVehicleId,
      fineCode: entity.fineCode,
      status: entity.status,
      date: entity.date,
      time: entity.time,
      enforcingAuthority: entity.enforcingAuthority,
      infractionCode: entity.infractionCode,
      description: entity.description,
      location: entity.location,
      value: entity.value,
      dueDate: entity.dueDate,
      barCode: entity.barCode,
    });
  }

  private toEntity(domain: VehicleFine): Partial<VehicleFineEntity> {
    return {
      id: domain.id,
      vehicleId: domain.vehicleId,
      preSaleImportHistoryVehicleId: domain.preSaleImportHistoryVehicleId,
      fineCode: domain.fineCode,
      status: domain.status,
      date: domain.date,
      time: domain.time,
      enforcingAuthority: domain.enforcingAuthority,
      infractionCode: domain.infractionCode,
      description: domain.description,
      location: domain.location,
      value: domain.value,
      dueDate: domain.dueDate,
      barCode: domain.barCode,
    };
  }

  async create(fine: VehicleFine): Promise<VehicleFine> {
    const entity = this.repository.create(this.toEntity(fine));
    const saved = await this.repository.save(entity);
    return this.toDomain(saved);
  }

  async getByPreSaleVehicleId(preSaleVehicleId: string): Promise<VehicleFine[]> {
    const results = await this.repository.findBy({ preSaleImportHistoryVehicleId: preSaleVehicleId });
    return results.map((entity) => this.toDomain(entity));
  }

  async getByVehicleId(vehicleId: string): Promise<VehicleFine[]> {
    const results = await this.repository.findBy({ vehicleId: vehicleId });
    return results.map((entity) => this.toDomain(entity));
  }
}
