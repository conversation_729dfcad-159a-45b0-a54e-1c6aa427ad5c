import { Inject, Injectable } from '@nestjs/common';
import { IVehicleFinesRepository } from '../repositories/vehicle-fine.repository.interface';
import { VehicleFine } from '../entities/vehicle-fine.entity';

type Input = { fine: VehicleFine };

@Injectable()
export class CreateVehicleFineUseCase {
  constructor(@Inject('IVehicleFinesRepository') private readonly vehicleFinesRepository: IVehicleFinesRepository) {}

  async execute({ fine }: Input) {
    return this.vehicleFinesRepository.create(fine);
  }
}
