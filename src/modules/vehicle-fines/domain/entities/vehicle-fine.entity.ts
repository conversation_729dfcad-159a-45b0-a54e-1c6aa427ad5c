import { BadRequestException } from '@nestjs/common';

const TIME_FORMAT_REGEX = /^([0-1][0-9]|2[0-3]):[0-5][0-9]:[0-5][0-9]$/;
const BARCODE_REGEX = /^\d{48}$/;

interface VehicleFineProps {
  id?: string;
  vehicleId: string;
  preSaleImportHistoryVehicleId?: string;
  fineCode: string;
  status: string;
  date: Date;
  time: string;
  enforcingAuthority: string;
  infractionCode: string;
  description: string;
  location: string;
  value: number;
  dueDate?: Date;
  barCode?: string;
}

export class VehicleFine {
  private readonly _id?: string;
  private readonly _vehicleId: string;
  private readonly _preSaleImportHistoryVehicleId?: string;
  private readonly _fineCode: string;
  private readonly _status: string;
  private readonly _date: Date;
  private readonly _time: string;
  private readonly _enforcingAuthority: string;
  private readonly _infractionCode: string;
  private readonly _description: string;
  private readonly _location: string;
  private readonly _value: number;
  private readonly _dueDate?: Date;
  private readonly _barCode?: string;

  private constructor(props: VehicleFineProps) {
    this._id = props.id;
    this._vehicleId = props.vehicleId;
    this._preSaleImportHistoryVehicleId = props.preSaleImportHistoryVehicleId;
    this._fineCode = props.fineCode;
    this._status = props.status;
    this._date = props.date;
    this._time = props.time;
    this._enforcingAuthority = props.enforcingAuthority;
    this._infractionCode = props.infractionCode;
    this._description = props.description;
    this._location = props.location;
    this._value = props.value;
    this._dueDate = props.dueDate;
    this._barCode = props.barCode;
  }

  static create(props: VehicleFineProps) {
    this.validateVehicleId(props.vehicleId);
    this.validateFineCode(props.fineCode);
    this.validateStatus(props.status);
    this.validateDate(props.date);
    this.validateTime(props.time);
    this.validateEnforcingAuthority(props.enforcingAuthority);
    this.validateInfractionCode(props.infractionCode);
    this.validateDescription(props.description);
    this.validateLocation(props.location);
    this.validateValue(props.value);
    this.validateDueDate(props.dueDate);
    this.validateBarCode(props.barCode);
    return new VehicleFine(props);
  }

  private static validateVehicleId(vehicleId: string) {
    if (!vehicleId) {
      throw new BadRequestException('ID do veículo é obrigatório');
    }
  }

  private static validateFineCode(fineCode: string) {
    if (!fineCode) {
      throw new BadRequestException('O código da multa é obrigatório');
    }
  }

  private static validateStatus(status: string) {
    if (!status) {
      throw new BadRequestException('O status da multa é obrigatório');
    }

    if (status.length > 20) {
      throw new BadRequestException('O status da multa não pode ter mais que 20 caracteres');
    }
  }

  private static validateDate(date: Date): void {
    if (!date) {
      throw new BadRequestException('A data da multa é obrigatória');
    }

    if (date < new Date(1900, 0, 1)) {
      throw new BadRequestException('Data da multa inválida');
    }
  }

  private static validateTime(time: string): void {
    if (!time) {
      throw new BadRequestException('A hora da multa é obrigatória');
    }

    if (!TIME_FORMAT_REGEX.test(time)) {
      throw new BadRequestException('Hora da multa deve estar no formato HH:MM:SS');
    }
  }

  private static validateEnforcingAuthority(enforcingAuthority: string) {
    if (!enforcingAuthority) {
      throw new BadRequestException('O órgão autuador é obrigatório');
    }

    if (enforcingAuthority.length > 100) {
      throw new BadRequestException('O nome do órgão autuador não pode ter mais que 100 caracteres');
    }
  }

  private static validateInfractionCode(infractionCode: string) {
    if (!infractionCode) {
      throw new BadRequestException('O código da infração é obrigatório');
    }

    if (infractionCode.length > 10) {
      throw new BadRequestException('O código da infração não pode ter mais que 10 caracteres');
    }
  }

  private static validateDescription(description: string) {
    if (!description) {
      throw new BadRequestException('A descrição é obrigatória');
    }

    if (description.length > 255) {
      throw new BadRequestException('A descrição da multa não pode ter mais que 255 caracteres');
    }
  }

  private static validateLocation(location: string) {
    if (!location) {
      throw new BadRequestException('A localização é obrigatória');
    }

    if (location.length > 255) {
      throw new BadRequestException('A localização não pode ter mais que 255 caracteres');
    }
  }

  private static validateValue(value: number): void {
    if (!isNaN(value) && value <= 0) {
      throw new BadRequestException('Valor deve ser maior que zero');
    }
  }

  private static validateDueDate(dueDate: Date): void {
    if (dueDate && dueDate < new Date(1900, 0, 1)) {
      throw new BadRequestException('Data de vencimento inválida');
    }
  }

  private static validateBarCode(barCode: string): void {
    if (barCode && !BARCODE_REGEX.test(barCode)) {
      throw new BadRequestException('O código de barras deve ter 48 dígitos');
    }
  }

  get id(): string {
    return this._id;
  }

  get vehicleId(): string {
    return this._vehicleId;
  }

  get preSaleImportHistoryVehicleId(): string {
    return this._preSaleImportHistoryVehicleId;
  }

  get fineCode(): string {
    return this._fineCode;
  }

  get status(): string {
    return this._status;
  }

  get date(): Date {
    return this._date;
  }

  get time(): string {
    return this._time;
  }

  get enforcingAuthority(): string {
    return this._enforcingAuthority;
  }

  get infractionCode(): string {
    return this._infractionCode;
  }

  get description(): string {
    return this._description;
  }

  get location(): string {
    return this._location;
  }

  get value(): number {
    return this._value;
  }

  get dueDate(): Date {
    return this._dueDate;
  }

  get barCode(): string {
    return this._barCode;
  }
}
