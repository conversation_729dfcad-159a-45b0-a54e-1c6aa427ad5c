import { DashboardGroupedByStateData } from '../value-objects/dashboard-grouped-by-state-data.vo';
import { BlockedByStatus } from '@/modules/dashboard/domain/value-objects/blocked-by-status.vo';
import { DashboardWeeklyQueries } from '@/modules/dashboard/domain/value-objects/dashboard-weekly-queries.vo';

export interface IDashboardRepository {
  getConsultedPlates(importHistoryId?: string): Promise<DashboardGroupedByStateData>;

  getUnblockedPlates(importHistoryId?: string): Promise<DashboardGroupedByStateData>;

  getBlockedPlates(importHistoryId?: string): Promise<DashboardGroupedByStateData>;

  getFinedPlates(importHistoryId?: string): Promise<DashboardGroupedByStateData>;

  getWeeklyQueries(): Promise<DashboardWeeklyQueries>;

  getBlockedByStatus(importHistoryId?: string): Promise<BlockedByStatus>;
}
