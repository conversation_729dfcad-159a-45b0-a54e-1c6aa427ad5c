type BlockedByStatusParams = {
  icms: number;
  outOfCirculation: number;
  invalidCsv: number;
  activeSale: number;
  financeReserve: number;
  manufacturerMediumDamage: number;
  administrativeUnavailabilityAccident: number;
  theftOccurrence: number;
  fiscalBenefit: number;
};

export class BlockedByStatus {
  public readonly icms: number;
  public readonly outOfCirculation: number;
  public readonly invalidCsv: number;
  public readonly activeSale: number;
  public readonly financeReserve: number;
  public readonly manufacturerMediumDamage: number;
  public readonly administrativeUnavailabilityAccident: number;
  public readonly theftOccurrence: number;
  public readonly fiscalBenefit: number;

  constructor(params: BlockedByStatusParams) {
    this.icms = params.icms;
    this.outOfCirculation = params.outOfCirculation;
    this.invalidCsv = params.invalidCsv;
    this.activeSale = params.activeSale;
    this.financeReserve = params.financeReserve;
    this.manufacturerMediumDamage = params.manufacturerMediumDamage;
    this.administrativeUnavailabilityAccident = params.administrativeUnavailabilityAccident;
    this.theftOccurrence = params.theftOccurrence;
    this.fiscalBenefit = params.fiscalBenefit;
  }
}
