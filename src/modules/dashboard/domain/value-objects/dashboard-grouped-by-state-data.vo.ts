import { DashboardStateData } from './dashboard-state-data.vo';

export class DashboardGroupedByStateData {
  private readonly total: number | string;
  private readonly states: DashboardStateData[];

  constructor(total: number | string, states: DashboardStateData[]) {
    this.total = total;
    this.states = states;
  }

  getTotal() {
    return this.total;
  }

  getStates() {
    return this.states;
  }
}
