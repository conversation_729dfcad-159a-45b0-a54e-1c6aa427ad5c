import { ApiProperty } from '@nestjs/swagger';
import { DashboardGroupedByStateDataDto } from './dashboard-grouped-by-state-data.dto';
import { DashboardWeeklyQueriesDataDto } from './dashboard-weekly-queries-data.dto';

export class DashboardDataDto {
  @ApiProperty({ description: 'Total de placas consultadas e top 3 estados', type: DashboardGroupedByStateDataDto })
  consultedPlates: DashboardGroupedByStateDataDto;

  @ApiProperty({
    description: 'Total de placas consultadas na situação VIGENTE (EM CIRCULACAO) e top 3 estados',
    type: DashboardGroupedByStateDataDto,
  })
  unblockedPlates: DashboardGroupedByStateDataDto;

  @ApiProperty({
    description: 'Total de placas consultadas fora da situação VIGENTE (EM CIRCULACAO) e top 3 estados',
    type: DashboardGroupedByStateDataDto,
  })
  blockedPlates: DashboardGroupedByStateDataDto;

  @ApiProperty({
    description: 'Total de multas registradas e top 3 estados',
    type: DashboardGroupedByStateDataDto,
  })
  finedPlates: DashboardGroupedByStateDataDto;

  @ApiProperty({
    description: 'Dados agrupados da semana atual',
    type: DashboardWeeklyQueriesDataDto,
  })
  weekly_queries: DashboardWeeklyQueriesDataDto;
}
