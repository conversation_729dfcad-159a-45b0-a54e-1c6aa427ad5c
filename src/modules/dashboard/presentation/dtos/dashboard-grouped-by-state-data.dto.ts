import { ApiProperty } from '@nestjs/swagger';
import { DashboardStateDataDto } from './dashboard-state-data.dto';
import { DashboardGroupedByStateData } from '../../domain/value-objects/dashboard-grouped-by-state-data.vo';

export class DashboardGroupedByStateDataDto {
  static fromDashboardGroupedByStateData(input: DashboardGroupedByStateData): DashboardGroupedByStateDataDto {
    return {
      total: input.getTotal() as number,
      byState: input.getStates()?.map((state) => ({ total: state.getValue() as number, state: state.getStateName() })),
    };
  }

  @ApiProperty({ description: 'O total de registros', example: 10 })
  total: number;

  @ApiProperty({ description: 'O total de resultados agrupados por estado', type: [DashboardStateDataDto] })
  byState: DashboardStateDataDto[];
}
