import { ApiProperty } from '@nestjs/swagger';
import { DashboardWeeklyBlockedDataDto } from './dashboard-weekly-blocked-data.dto';

export class DashboardWeeklyQueriesDataDto {
  @ApiProperty({ description: 'Totais de bloqueios por status', type: DashboardWeeklyBlockedDataDto })
  blocked: DashboardWeeklyBlockedDataDto;

  @ApiProperty({
    description: 'O total de consultas realizadas nos dias da semana atual',
    example: [10, 20, 30, 40, 50, 60, 70],
  })
  queries_this_week: number[];
}
