import { ApiProperty } from '@nestjs/swagger';
import { BlockedByStatus } from '../../domain/value-objects/blocked-by-status.vo';

export class DashboardWeeklyBlockedDataDto {
  static fromBlockedByStatus(input: BlockedByStatus): DashboardWeeklyBlockedDataDto {
    const {
      icms,
      activeSale,
      administrativeUnavailabilityAccident,
      manufacturerMediumDamage,
      financeReserve,
      outOfCirculation,
      theftOccurrence,
      invalidCsv,
      fiscalBenefit,
    } = input;
    return {
      icms,
      active_sale: activeSale,
      administrative_unavailability_accident: administrativeUnavailabilityAccident,
      finance_reserve: financeReserve,
      fiscal_benefit: fiscalBenefit,
      invalid_csv: invalidCsv,
      manufacturer_medium_damage: manufacturerMediumDamage,
      out_of_circulation: outOfCirculation,
      theft_occurrence: theftOccurrence,
    };
  }

  @ApiProperty({ description: 'Total de veículos bloqueados por icms', example: 10 })
  icms: number;

  @ApiProperty({ description: 'Total de veículos bloqueados por estarem fora de circulação', example: 10 })
  out_of_circulation: number;

  @ApiProperty({ description: 'Total de veículos bloqueados por estarem com csv inválido', example: 10 })
  invalid_csv: number;

  @ApiProperty({ description: 'Total de veículos bloqueados por estarem com venda ativa', example: 10 })
  active_sale: number;

  @ApiProperty({ description: 'Total de veículos bloqueados por estarem com reserva financeira', example: 10 })
  finance_reserve: number;

  @ApiProperty({
    description: 'Total de veículos bloqueados por estarem com registro de dano médio do fabricante',
    example: 10,
  })
  manufacturer_medium_damage: number;

  @ApiProperty({
    description: 'Total de veículos bloqueados por estarem com indisponibilidade administrativa ou acidente',
    example: 10,
  })
  administrative_unavailability_accident: number;

  @ApiProperty({ description: 'Total de veículos bloqueados por estarem com ocorrência de roubo', example: 10 })
  theft_occurrence: number;

  @ApiProperty({ description: 'Total de veículos bloqueados por estarem com benefício fiscal', example: 10 })
  fiscal_benefit: number;
}
