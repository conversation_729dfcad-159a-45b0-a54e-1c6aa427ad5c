import { Controller, Get, Param, ParseUUIDPipe } from '@nestjs/common';
import { ApiOperation, ApiParam, ApiResponse, ApiTags } from '@nestjs/swagger';
import { GetDashboardDataUseCase } from '../application/get-dashboard-data.use-case';
import { DashboardDataDto } from './dtos/dashboard-data.dto';

@ApiTags('Dashboard')
@Controller('dashboard')
export class DashboardController {
  constructor(private readonly getDashboardDataUseCase: GetDashboardDataUseCase) {}

  @Get('/:importHistoryId?')
  @ApiOperation({ summary: 'Dashboard' })
  @ApiParam({
    name: 'importHistoryId',
    required: false,
    description: 'O UUID de uma importação para filtrar os dados retornados',
  })
  @ApiResponse({ status: 200, description: 'Modelo de dados para o dashboard principal', type: DashboardDataDto })
  async getDashboardDataV2(
    @Param(
      'importHistoryId',
      new ParseUUIDPipe({
        optional: true,
      }),
    )
    importHistoryId?: string,
  ) {
    return this.getDashboardDataUseCase.execute({ importHistoryId });
  }
}
