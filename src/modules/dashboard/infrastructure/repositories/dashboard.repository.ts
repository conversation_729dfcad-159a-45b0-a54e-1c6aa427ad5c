import { Injectable } from '@nestjs/common';
import { IDashboardRepository } from '../../domain/repositories/dashboard.repository.interface';
import { DashboardGroupedByStateData } from '../../domain/value-objects/dashboard-grouped-by-state-data.vo';
import { DashboardWeeklyQueries } from '../../domain/value-objects/dashboard-weekly-queries.vo';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { PreSaleImportHistoryVehicleEntity } from '@/modules/pre-sales/infrastructure/entities/pre-sale-import-history-vehicle.entity';
import { DashboardStateData } from '../../domain/value-objects/dashboard-state-data.vo';
import { VehicleFineEntity } from '@/modules/vehicle-fines/infrastructure/entities/vehicle-fine.entity';
import { addDays, endOfWeek, format, startOfWeek } from 'date-fns';
import { BlockedByStatus } from '@/modules/dashboard/domain/value-objects/blocked-by-status.vo';
import { VehicleEntity } from '@/modules/vehicles/infrastructure/vehicle.entity';

const IN_CIRCULATON_STATUS = 'VIGENTE (EM CIRCULACAO)';

const ICMS_BLOCKED_STATUS = 'ICMS';
const OUT_OF_CIRCULATION_STATUS = 'VENCIDO (EM CIRCULACAO)';
const INVALID_CSV_STATUS = 'CSV INVALIDO';
const ACTIVE_SALE_STATUS = 'VENDA EM ANDAMENTO';
const FINANCE_RESERVE_STATUS = 'RESERVA FINANCEIRA';
const MANUFACTURER_MEDIUM_DAMAGE_STATUS = 'MEDIDA DO FABRICANTE';
const ADMINISTRATIVE_UNAVAILABILITY_ACCIDENT_STATUS = 'INDISPONIBILIDADE ADMINISTRATIVA';
const THEFT_OCCURRENCE_STATUS = 'VEICULO ROUBADO, JA RECUPERADO';
const FISCAL_BENEFIT_STATUS = 'BENEFICIO FISCAL';

type GroupedByStateRawData = { state: string; total: string };

@Injectable()
export class DashboardRepository implements IDashboardRepository {
  constructor(
    @InjectRepository(VehicleEntity)
    private readonly vehicleRepository: Repository<VehicleEntity>,
    @InjectRepository(PreSaleImportHistoryVehicleEntity)
    private readonly importHistoryVehicleRepository: Repository<PreSaleImportHistoryVehicleEntity>,
    @InjectRepository(VehicleFineEntity)
    private readonly vehicleFineRepository: Repository<VehicleFineEntity>,
  ) {}

  async getUnblockedPlates(importHistoryId?: string): Promise<DashboardGroupedByStateData> {
    const queryBuilder = this.vehicleRepository
      .createQueryBuilder('vehicle')
      .innerJoin('pre_sale_import_history_vehicles', 'historyVehicle', 'historyVehicle.vehicle_id = vehicle.id')
      .innerJoin('pre_sale_import_history', 'history', 'history.id = historyVehicle.import_history_id')
      .select('vehicle.state', 'state')
      .addSelect('COUNT(DISTINCT vehicle.id)', 'total')
      .groupBy('vehicle.state')
      .where('vehicle.situation = :situation', { situation: IN_CIRCULATON_STATUS })
      .orderBy('total', 'DESC')
      .limit(3);

    if (importHistoryId) {
      queryBuilder.andWhere('history.id = :importHistoryId', { importHistoryId });
    }

    const result = await queryBuilder.getRawMany<GroupedByStateRawData>();
    return this.processGroupedByStateQueryResult(result);
  }

  async getBlockedPlates(importHistoryId?: string): Promise<DashboardGroupedByStateData> {
    const queryBuilder = this.vehicleRepository
      .createQueryBuilder('vehicle')
      .innerJoin('pre_sale_import_history_vehicles', 'historyVehicle', 'historyVehicle.vehicle_id = vehicle.id')
      .innerJoin('pre_sale_import_history', 'history', 'history.id = historyVehicle.import_history_id')
      .select('vehicle.state', 'state')
      .addSelect('COUNT(DISTINCT vehicle.id)', 'total')
      .groupBy('vehicle.state')
      .where('vehicle.situation != :situation', { situation: IN_CIRCULATON_STATUS })
      .orderBy('total', 'DESC')
      .limit(3);

    if (importHistoryId) {
      queryBuilder.andWhere('history.id = :importHistoryId', { importHistoryId });
    }

    const result = await queryBuilder.getRawMany<GroupedByStateRawData>();
    return this.processGroupedByStateQueryResult(result);
  }

  async getConsultedPlates(importHistoryId?: string): Promise<DashboardGroupedByStateData> {
    const queryBuilder = this.vehicleRepository
      .createQueryBuilder('vehicle')
      .innerJoin('pre_sale_import_history_vehicles', 'historyVehicle', 'historyVehicle.vehicle_id = vehicle.id')
      .innerJoin('pre_sale_import_history', 'history', 'history.id = historyVehicle.import_history_id')
      .select('vehicle.state', 'state')
      .addSelect('COUNT(DISTINCT vehicle.id)', 'total')
      .groupBy('vehicle.state')
      .orderBy('total', 'DESC');

    if (importHistoryId) {
      queryBuilder.andWhere('history.id = :importHistoryId', { importHistoryId });
    }

    const result = await queryBuilder.getRawMany<GroupedByStateRawData>();
    return this.processGroupedByStateQueryResult(result);
  }

  async getFinedPlates(importHistoryId?: string): Promise<DashboardGroupedByStateData> {
    const queryBuilder = this.vehicleFineRepository
      .createQueryBuilder('vehicleFine')
      .leftJoin('vehicleFine.vehicle', 'vehicle')
      .select('vehicle.state', 'state')
      .addSelect('COUNT(DISTINCT vehicle.id)', 'total')
      .groupBy('vehicle.state')
      .orderBy('total', 'DESC')
      .limit(3);

    if (importHistoryId) {
      queryBuilder
        .innerJoin('vehicleFine.preSaleImportHistoryVehicle', 'importHistoryVehicle')
        .where('importHistoryVehicle.importHistoryId = :importHistoryId', {
          importHistoryId,
        });
    }

    const result = await queryBuilder.getRawMany<GroupedByStateRawData>();
    return this.processGroupedByStateQueryResult(result);
  }

  async getWeeklyQueries(): Promise<DashboardWeeklyQueries> {
    const updatesByWeekDay = await this.countQueriesByWeekDay();
    return new DashboardWeeklyQueries(updatesByWeekDay);
  }

  async getBlockedByStatus(importHistoryId?: string): Promise<BlockedByStatus> {
    const icms = await this.getWeeklyBlockedBySituation(ICMS_BLOCKED_STATUS, importHistoryId);
    const outOfCirculation = await this.getWeeklyBlockedBySituation(OUT_OF_CIRCULATION_STATUS, importHistoryId);
    const invalidCsv = await this.getWeeklyBlockedBySituation(INVALID_CSV_STATUS, importHistoryId);
    const activeSale = await this.getWeeklyBlockedBySituation(ACTIVE_SALE_STATUS, importHistoryId);
    const financeReserve = await this.getWeeklyBlockedBySituation(FINANCE_RESERVE_STATUS, importHistoryId);
    const manufacturerMediumDamage = await this.getWeeklyBlockedBySituation(MANUFACTURER_MEDIUM_DAMAGE_STATUS, importHistoryId);
    const administrativeUnavailabilityAccident = await this.getWeeklyBlockedBySituation(ADMINISTRATIVE_UNAVAILABILITY_ACCIDENT_STATUS, importHistoryId);
    const theftOccurrence = await this.getWeeklyBlockedBySituation(THEFT_OCCURRENCE_STATUS, importHistoryId);
    const fiscalBenefit = await this.getWeeklyBlockedBySituation(FISCAL_BENEFIT_STATUS, importHistoryId);

    return new BlockedByStatus({
      icms,
      outOfCirculation,
      invalidCsv,
      activeSale,
      financeReserve,
      manufacturerMediumDamage,
      administrativeUnavailabilityAccident,
      theftOccurrence,
      fiscalBenefit,
    });
  }

  private processGroupedByStateQueryResult(result: GroupedByStateRawData[]): DashboardGroupedByStateData {
    let overallTotal = 0;
    const byState: DashboardStateData[] = [];

    result.forEach((item) => {
      overallTotal += Number(item.total);
      byState.push(new DashboardStateData(item.state, Number(item.total)));
    });

    return new DashboardGroupedByStateData(overallTotal, byState);
  }

  private getWeeklyBlockedBySituation(situation: string, importHistoryId?: string) {
    const currentWeekStart = startOfWeek(new Date());

    const queryBuilder = this.importHistoryVehicleRepository
      .createQueryBuilder('importHistoryVehicle')
      .leftJoin('importHistoryVehicle.vehicle', 'vehicle')
      .where('vehicle.situation = :situation', { situation })
      .andWhere('importHistoryVehicle.updatedAt >= :currentWeekStart', { currentWeekStart });

    if (importHistoryId) {
      queryBuilder.andWhere('importHistoryVehicle.importHistoryId = :importHistoryId', { importHistoryId });
    }

    return queryBuilder.getCount();
  }

  private async countQueriesByWeekDay() {
    const currentWeekStart = startOfWeek(new Date(), { weekStartsOn: 1 });
    const currentWeekEnd = endOfWeek(new Date(), { weekStartsOn: 1 });
    const weekDays = Array.from({ length: 7 }, (_, i) => {
      return format(addDays(currentWeekStart, i), 'yyyy-MM-dd');
    });

    const result = await this.importHistoryVehicleRepository
      .createQueryBuilder('importHistoryVehicle')
      .select("TO_CHAR(importHistoryVehicle.updatedAt, 'YYYY-MM-DD')", 'day')
      .addSelect('COUNT(*)', 'total')
      .where('importHistoryVehicle.updatedAt >= :currentWeekStart', { currentWeekStart })
      .andWhere('importHistoryVehicle.updatedAt <= :currentWeekEnd', { currentWeekEnd })
      .groupBy('day')
      .orderBy('day', 'ASC')
      .getRawMany<{ day: string; total: string }>();

    const totalsByDay: Record<string, number> = {};
    result.forEach((row) => {
      totalsByDay[row.day] = Number(row.total);
    });

    return weekDays.map((weekDay) => totalsByDay[weekDay] ?? 0);
  }
}
