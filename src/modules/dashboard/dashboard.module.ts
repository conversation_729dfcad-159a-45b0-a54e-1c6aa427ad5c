import { Modu<PERSON> } from '@nestjs/common';
import { DashboardController } from './presentation/dashboard.controller';
import { DASHBOARD_PROVIDERS } from './dashboard.providers';
import { TypeOrmModule } from '@nestjs/typeorm';
import { PreSaleImportHistoryVehicleEntity } from '@/modules/pre-sales/infrastructure/entities/pre-sale-import-history-vehicle.entity';
import { VehicleFineEntity } from '@/modules/vehicle-fines/infrastructure/entities/vehicle-fine.entity';
import { VehicleEntity } from '@/modules/vehicles/infrastructure/vehicle.entity';

@Module({
  controllers: [DashboardController],
  providers: DASHBOARD_PROVIDERS,
  imports: [TypeOrmModule.forFeature([VehicleEntity, PreSaleImportHistoryVehicleEntity, VehicleFineEntity])],
})
export class DashboardModule {}
