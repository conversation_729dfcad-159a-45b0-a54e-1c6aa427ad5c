import { Inject, Injectable } from '@nestjs/common';
import { IDashboardRepository } from '../domain/repositories/dashboard.repository.interface';
import { DashboardWeeklyQueries } from '../domain/value-objects/dashboard-weekly-queries.vo';
import { DashboardDataDto } from '../presentation/dtos/dashboard-data.dto';
import { DashboardGroupedByStateDataDto } from '../presentation/dtos/dashboard-grouped-by-state-data.dto';
import { DashboardWeeklyBlockedDataDto } from '../presentation/dtos/dashboard-weekly-blocked-data.dto';

type Input = {
  importHistoryId?: string;
};

@Injectable()
export class GetDashboardDataUseCase {
  constructor(@Inject('IDashboardRepository') private readonly dashboardRepository: IDashboardRepository) {}

  async execute({ importHistoryId }: Input): Promise<DashboardDataDto> {
    const consultedPlates = await this.dashboardRepository.getConsultedPlates(importHistoryId);
    const unblockedPlates = await this.dashboardRepository.getUnblockedPlates(importHistoryId);
    const blockedPlates = await this.dashboardRepository.getBlockedPlates(importHistoryId);
    const finedPlates = await this.dashboardRepository.getFinedPlates(importHistoryId);
    const blockedByStatus = await this.dashboardRepository.getBlockedByStatus(importHistoryId);

    let weeklyQueries: DashboardWeeklyQueries;
    if (!importHistoryId) {
      weeklyQueries = await this.dashboardRepository.getWeeklyQueries();
    }

    return {
      consultedPlates: DashboardGroupedByStateDataDto.fromDashboardGroupedByStateData(consultedPlates),
      unblockedPlates: DashboardGroupedByStateDataDto.fromDashboardGroupedByStateData(unblockedPlates),
      blockedPlates: DashboardGroupedByStateDataDto.fromDashboardGroupedByStateData(blockedPlates),
      finedPlates: DashboardGroupedByStateDataDto.fromDashboardGroupedByStateData(finedPlates),
      weekly_queries: {
        blocked: DashboardWeeklyBlockedDataDto.fromBlockedByStatus(blockedByStatus),
        queries_this_week: weeklyQueries?.getValue() ?? [],
      },
    };
  }
}
