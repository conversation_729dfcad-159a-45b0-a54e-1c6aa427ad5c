-- public.vehicle_extra definition

-- Drop table

-- DROP TABLE public.vehicle_extra;

CREATE TABLE public.vehicle_extra (
	id uuid NOT NULL,
	presale_import_history_vehicle_id uuid NOT NULL,
	extras json NULL,
	extras_gravame json NULL,
	extras_recall json NULL,
	extras_owner json NULL,
	extras_block_pr json NULL,
	created_at timestamp NOT NULL,
	updated_at timestamp NOT NULL,
	deleted_at timestamp NULL,
	CONSTRAINT vehicle_extra_pkey PRIMARY KEY (id)
);
CREATE INDEX ix_vehicle_extra_presale_import_history_vehicle_id ON public.vehicle_extra USING btree (presale_import_history_vehicle_id);


-- public.vehicle_extra foreign keys

ALTER TABLE public.vehicle_extra ADD CONSTRAINT vehicle_extra_presale_import_history_vehicle_id_fkey FOREIGN KEY (presale_import_history_vehicle_id) REFERENCES public.pre_sale_import_history_vehicles(id) ON DELETE CASCADE;



-- public.vehicle_extra_fines definition

-- Drop table

-- DROP TABLE public.vehicle_extra_fines;

CREATE TABLE public.vehicle_extra_fines (
	id uuid NOT NULL,
	vehicle_debit_id uuid NOT NULL,
	extras json NULL,
	created_at timestamp NOT NULL,
	updated_at timestamp NOT NULL,
	deleted_at timestamp NULL,
	CONSTRAINT vehicle_extra_fines_pkey PRIMARY KEY (id)
);
CREATE INDEX ix_vehicle_extra_fines_vehicle_debit_id ON public.vehicle_extra_fines USING btree (vehicle_debit_id);


-- public.vehicle_extra_fines foreign keys

ALTER TABLE public.vehicle_extra_fines ADD CONSTRAINT vehicle_extra_fines_vehicle_debit_id_fkey FOREIGN KEY (vehicle_debit_id) REFERENCES public.vehicle_debits(id) ON DELETE CASCADE;