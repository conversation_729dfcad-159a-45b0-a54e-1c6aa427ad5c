import { FilterOperator, PaginateConfig } from 'nestjs-paginate';
import { VehicleEntity } from './vehicle.entity';

export const VEHICLE_PAGINATE_CONFIG: PaginateConfig<VehicleEntity> = {
  defaultSortBy: [['createdAt', 'DESC']],
  sortableColumns: ['renavam', 'chassi', 'numberPlate', 'state', 'brandModel', 'modelYear', 'manufactureYear', 'color', 'fuelType', 'createdAt'],
  searchableColumns: ['renavam', 'chassi', 'numberPlate', 'state', 'brandModel', 'modelYear', 'manufactureYear', 'color', 'fuelType', 'createdAt'],
  filterableColumns: {
    renavam: [FilterOperator.ILIKE],
    chassi: [FilterOperator.ILIKE],
    numberPlate: [FilterOperator.ILIKE],
    state: [FilterOperator.ILIKE],
    brandModel: [FilterOperator.ILIKE],
    modelYear: [FilterOperator.ILIKE],
    manufactureYear: [FilterOperator.ILIKE],
    color: [FilterOperator.ILIKE],
    fuelType: [FilterOperator.ILIKE],
  },
};
