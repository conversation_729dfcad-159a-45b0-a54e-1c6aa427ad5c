import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { paginate, Paginated, PaginateQuery } from 'nestjs-paginate';
import { Repository } from 'typeorm';
import { IVehicleRepository } from '../domain/vehicle.repository.interface';
import { VehicleCreateDto } from '../presentation/vehicle.dto';
import { VehicleEntity } from './vehicle.entity';
import { VEHICLE_PAGINATE_CONFIG } from './vehicle.query';
import { Vehicle } from '@/modules/vehicles/domain/vehicle.entity';

@Injectable()
export class VehicleRepository implements IVehicleRepository {
  constructor(
    @InjectRepository(VehicleEntity)
    private readonly repository: Repository<VehicleEntity>,
  ) {}

  private toDomain(entity: VehicleEntity): Vehicle {
    return Vehicle.create({
      id: entity.id,
      licensePlate: entity.numberPlate,
      chassi: entity.chassi,
      renavam: entity.renavam,
      state: entity.state,
      city: entity.city,
      category: entity.category,
      situation: entity.situation,
      gravameStatus: entity.gravameStatus,
      gravameSituation: entity.gravameSituation,
      owner: entity.owner,
      ownerDocument: entity.ownerDocument,
      lastCsvReport: entity.lastCsvReport,
      regularizationDeadline: entity.regularizationDeadline,
      lastLicensing: entity.lastLicensing,
    });
  }

  private toEntity(domain: Vehicle): Partial<VehicleEntity> {
    return {
      id: domain.getId(),
      numberPlate: domain.getLicensePlate(),
      chassi: domain.getChassis(),
      renavam: domain.getRenavam(),
      state: domain.getState(),
      city: domain.getCity(),
      category: domain.getCategory(),
      situation: domain.getSituation(),
      gravameStatus: domain.getGravameStatus(),
      gravameSituation: domain.getGravameSituation(),
      owner: domain.getOwner(),
      ownerDocument: domain.getOwnerDocument(),
      lastCsvReport: domain.getLastCsvReport(),
      regularizationDeadline: domain.getRegularizationDeadline(),
      lastLicensing: domain.getLastLicensing(),
    };
  }

  async create(vehicle: VehicleCreateDto): Promise<VehicleEntity> {
    const entity = this.repository.create(vehicle);
    return await this.repository.save(entity);
  }

  async findOneById(id: string): Promise<VehicleEntity> {
    return await this.repository.findOneByOrFail({ id });
  }

  async findByPlateOrChassisOrRenavam(plate?: string, chassis?: string, renavam?: string): Promise<VehicleEntity> {
    const query = this.repository.createQueryBuilder('vehicle');

    if (plate) {
      query.orWhere(`vehicle.numberPlate = :plate`, { plate });
    }
    if (chassis) {
      query.orWhere(`vehicle.chassi = :chassis`, { chassis });
    }
    if (renavam) {
      query.orWhere(`vehicle.renavam = :renavam`, { renavam });
    }

    return await query.getOne();
  }

  async findAll(query: PaginateQuery): Promise<Paginated<VehicleEntity>> {
    return await paginate(query, this.repository, VEHICLE_PAGINATE_CONFIG);
  }

  async update(id: string, data: Partial<VehicleCreateDto>): Promise<void> {
    await this.repository.update({ id }, data);
  }

  async updateAfterProcessing(id: string, vehicle: Vehicle): Promise<void> {
    const entity = this.toEntity(vehicle);
    await this.repository.update({ id }, entity);
  }

  async delete(id: string): Promise<void> {
    await this.repository.softDelete(id);
  }
}
