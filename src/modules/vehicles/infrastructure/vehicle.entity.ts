import { BaseEntity } from '@/core/infrastructure/common/abstracts/entity.abstract';
import { Column, Entity, Index, OneToMany, PrimaryGeneratedColumn, Unique } from 'typeorm';
import { VehicleDebitEntity } from '@/modules/vehicle-debits/infrastructure/entities/vehicle-debit.entity';
import { VehicleFineEntity } from '@/modules/vehicle-fines/infrastructure/entities/vehicle-fine.entity';
import { VehicleRecallEntity } from '@/modules/vehicle-recalls/infrastructure/entities/vehicle-recall.entity';

@Entity('vehicles')
@Unique(['renavam']) // Garante que o renavam seja único
@Unique(['chassi']) // Garante que o chassi seja único
@Unique(['numberPlate']) // Garante que a placa seja única
export class VehicleEntity extends BaseEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Index()
  @Column({ length: 11 })
  renavam: string;

  @Index()
  @Column({ length: 17 })
  chassi: string;

  @Column({ name: 'brand_model', nullable: true })
  brandModel?: string;

  @Column({ name: 'model_year', nullable: true })
  modelYear?: number;

  @Column({ name: 'manufacture_year', nullable: true })
  manufactureYear?: number;

  @Column({ nullable: true })
  color?: string;

  @Column({ name: 'fuel_type', nullable: true })
  fuelType?: string;

  @Index()
  @Column({ name: 'number_plate', length: 7 })
  numberPlate: string;

  @Column({ length: 2, default: 'PR' }) // Define um valor padrão para evitar NULLs inesperados
  state: string;

  @Column({ nullable: true })
  city?: string;

  @Column({ nullable: true })
  category?: string;

  @Column({ nullable: true })
  situation?: string;

  @Column({ name: 'gravame_status', nullable: true })
  gravameStatus?: string;

  @Column({ name: 'gravame_situation', nullable: true })
  gravameSituation?: string;

  @Column({ nullable: true })
  owner?: string;

  @Column({ name: 'owner_document', nullable: true })
  ownerDocument?: string;

  @Column({ name: 'last_csv_report', nullable: true })
  lastCsvReport?: Date;

  @Column({ name: 'regularization_deadline', nullable: true })
  regularizationDeadline?: Date;

  @Column({ name: 'blocking_reason', nullable: true })
  blockingReason?: string;

  @Column({ name: 'last_licensing', nullable: true })
  lastLicensing?: string;

  @OneToMany(() => VehicleDebitEntity, (vehicleDebit) => vehicleDebit.vehicle)
  debits: VehicleDebitEntity[];

  @OneToMany(() => VehicleFineEntity, (vehicleFine) => vehicleFine.vehicle)
  fines: VehicleFineEntity[];

  @OneToMany(() => VehicleRecallEntity, (vehicleRecall) => vehicleRecall.vehicle)
  recalls: VehicleRecallEntity[];
}
