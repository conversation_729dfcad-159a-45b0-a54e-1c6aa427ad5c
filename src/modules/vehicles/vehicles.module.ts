import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { VehicleEntity } from './infrastructure/vehicle.entity';
import { VehiclesController } from './presentation/vehicles.controller';
import { VEHICLES_EXPORTS, VEHICLES_PROVIDERS } from './vehicles.providers';

/**
 * <PERSON><PERSON><PERSON>lo responsável por gerenciar funcionalidades relacionadas a veículos
 */
@Module({
  imports: [TypeOrmModule.forFeature([VehicleEntity])],
  controllers: [VehiclesController],
  providers: [...VEHICLES_PROVIDERS],
  exports: [...VEHICLES_EXPORTS],
})
export class VehiclesModule {}
