import { CreateVehicleUseCase } from './application/create-vehicle.use-case';
import { FindAllVehiclesUseCase } from './application/find-all-vehicles.use-case';
import { FindVehicleByIdUseCase } from './application/find-vehicle-by-id.use-case';
import { FindVehicleByPlateChassisRenavamUseCase } from './application/find-vehicle-by-plate-chassis-renavam.use-case';
import { RemoveVehicleUseCase } from './application/remove-vehicle.use-case';
import { UpdateVehicleUseCase } from './application/update-vehicle.use-case';
import { VehicleRepository } from './infrastructure/vehicle.repository';

const REPOSITORIES = [
  {
    provide: 'IVehicleRepository',
    useClass: VehicleRepository,
  },
];

export const VEHICLES_PROVIDERS = [
  // UseCases
  CreateVehicleUseCase,
  FindAllVehiclesUseCase,
  FindVehicleByIdUseCase,
  FindVehicleByPlateChassisRenavamUseCase,
  UpdateVehicleUseCase,
  RemoveVehicleUseCase,
  // Repositories
  ...REPOSITORIES,
];

export const VEHICLES_EXPORTS = [...REPOSITORIES, CreateVehicleUseCase, FindVehicleByPlateChassisRenavamUseCase];
