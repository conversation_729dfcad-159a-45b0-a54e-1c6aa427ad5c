import { Chassi } from './value-objects/chassi';
import { LicensePlate } from './value-objects/license-plate.vo';
import { Renavam } from './value-objects/renavam.vo';
import { StateUF } from './value-objects/state-uf.vo';
import { VehicleFine } from '@/modules/vehicle-fines/domain/entities/vehicle-fine.entity';
import { VehicleDebit } from '@/modules/vehicle-debits/domain/entities/vehicle-debit.entity';
import { VehicleRecall } from '@/modules/vehicle-recalls/domain/entities/vehicle-recall.entity';

type VehicleProps = {
  id?: string;
  licensePlate: string;
  chassi: string;
  renavam: string;
  state?: string;
  city?: string;
  category?: string;
  situation?: string;
  gravameStatus?: string;
  gravameSituation?: string;
  owner?: string;
  ownerDocument?: string;
  lastCsvReport?: Date;
  regularizationDeadline?: Date;
  lastLicensing?: string;
  recall?: string;
  recallDescription?: string;
  recallStatus?: string;
  recallRegistrationDate?: Date;
  recallLimitDate?: Date;
};

export class Vehicle {
  private readonly id: string;
  private readonly licensePlate: LicensePlate;
  private readonly chassi: Chassi;
  private readonly renavam: Renavam;
  private readonly state: StateUF;
  private readonly city: string;
  private readonly category: string;
  private readonly situation: string;
  private readonly gravameStatus: string;
  private readonly gravameSituation: string;
  private readonly owner: string;
  private readonly ownerDocument: string;
  private readonly lastCsvReport: Date;
  private readonly regularizationDeadline: Date;
  private readonly lastLicensing: string;

  private readonly fines: VehicleFine[] = [];
  private readonly debits: VehicleDebit[] = [];
  private readonly recalls: VehicleRecall[] = [];

  constructor(props: VehicleProps) {
    this.id = props.id;
    this.licensePlate = new LicensePlate(props.licensePlate);
    this.chassi = new Chassi(props.chassi);
    this.renavam = new Renavam(props.renavam);
    this.state = new StateUF(props.state);
    this.city = props.city;
    this.category = props.category;
    this.situation = props.situation;
    this.gravameStatus = props.gravameStatus;
    this.gravameSituation = props.gravameSituation;
    this.owner = props.owner;
    this.ownerDocument = props.ownerDocument;
    this.lastCsvReport = props.lastCsvReport;
    this.regularizationDeadline = props.regularizationDeadline;
    this.lastLicensing = props.lastLicensing;
  }

  static create(props: VehicleProps): Vehicle {
    return new Vehicle(props);
  }

  getId() {
    return this.id;
  }

  getLicensePlate(): string {
    return this.licensePlate.getValue();
  }

  getChassis(): string {
    return this.chassi.getValue();
  }

  getRenavam(): string {
    return this.renavam.getValue();
  }

  getState(): string {
    return this.state.getValue();
  }

  getCity(): string {
    return this.city;
  }

  getCategory(): string {
    return this.category;
  }

  getSituation(): string {
    return this.situation;
  }

  getGravameStatus(): string {
    return this.gravameStatus;
  }

  getGravameSituation(): string {
    return this.gravameSituation;
  }

  getOwner(): string {
    return this.owner;
  }

  getOwnerDocument(): string {
    return this.ownerDocument;
  }

  getLastCsvReport(): Date {
    return this.lastCsvReport;
  }

  getRegularizationDeadline(): Date {
    return this.regularizationDeadline;
  }

  getLastLicensing(): string {
    return this.lastLicensing;
  }

  addFine(fine: VehicleFine) {
    if (!fine) {
      return;
    }
    this.fines.push(fine);
  }

  getFines(): VehicleFine[] {
    return [...this.fines];
  }

  addDebit(debit: VehicleDebit) {
    if (!debit) {
      return;
    }
    this.debits.push(debit);
  }

  getDebits(): VehicleDebit[] {
    return [...this.debits];
  }

  addRecall(recall: VehicleRecall) {
    if (!recall) {
      return;
    }
    this.recalls.push(recall);
  }

  getRecalls(): VehicleRecall[] {
    return [...this.recalls];
  }
}
