import { Paginated, PaginateQuery } from 'nestjs-paginate';
import { VehicleEntity } from '../infrastructure/vehicle.entity';
import { VehicleCreateDto } from '../presentation/vehicle.dto';
import { Vehicle } from './vehicle.entity';

export interface IVehicleRepository {
  create(vehicle: VehicleCreateDto): Promise<VehicleEntity>;

  findOneById(id: string): Promise<VehicleEntity>;

  findByPlateOrChassisOrRenavam(plate?: string, chassis?: string, renavam?: string): Promise<VehicleEntity>;

  findAll(query: PaginateQuery): Promise<Paginated<VehicleEntity>>;

  update(id: string, data: Partial<VehicleCreateDto>): Promise<void>;

  updateAfterProcessing(id: string, vehicle: Vehicle): Promise<void>;

  delete(id: string): Promise<void>;
}
