import { Body, Controller, Delete, Get, Param, Patch, Post, Put } from '@nestjs/common';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { plainToInstance } from 'class-transformer';
import { Roles } from 'nest-keycloak-connect';
import { Paginate, Paginated, PaginatedSwaggerDocs, PaginateQuery } from 'nestjs-paginate';
import { CreateVehicleUseCase } from '../application/create-vehicle.use-case';
import { FindAllVehiclesUseCase } from '../application/find-all-vehicles.use-case';
import { FindVehicleByIdUseCase } from '../application/find-vehicle-by-id.use-case';
import { RemoveVehicleUseCase } from '../application/remove-vehicle.use-case';
import { UpdateVehicleUseCase } from '../application/update-vehicle.use-case';
import { VehicleEntity } from '../infrastructure/vehicle.entity';
import { VEHICLE_PAGINATE_CONFIG } from '../infrastructure/vehicle.query';
import { VehicleCreateDto, VehicleResponseDto } from './vehicle.dto';

@ApiTags('Veículos')
@Controller('vehicles')
export class VehiclesController {
  constructor(
    private readonly createVehicleUseCase: CreateVehicleUseCase,
    private readonly findAllVehiclesUseCase: FindAllVehiclesUseCase,
    private readonly findVehicleByIdUseCase: FindVehicleByIdUseCase,
    private readonly updateVehicleUseCase: UpdateVehicleUseCase,
    private readonly removeVehicleUseCase: RemoveVehicleUseCase,
  ) {}

  @Post()
  @ApiOperation({ summary: 'Criar um novo veículo' })
  @ApiResponse({ status: 201, description: 'Veículo criado com sucesso', type: VehicleResponseDto })
  @ApiResponse({ status: 400, description: 'Dados inválidos' })
  async create(@Body() createVehicleDto: VehicleCreateDto) {
    const vehicle = await this.createVehicleUseCase.execute(createVehicleDto);
    return plainToInstance(VehicleResponseDto, vehicle);
  }

  @Get()
  @ApiOperation({ summary: 'Listar todos os veículos com paginação' })
  @PaginatedSwaggerDocs(VehicleResponseDto, VEHICLE_PAGINATE_CONFIG)
  async findAll(
    @Paginate()
    query: PaginateQuery,
  ) {
    const result: Paginated<VehicleEntity> = await this.findAllVehiclesUseCase.execute(query);

    return {
      ...result,
      data: result.data.map((vehicle) => plainToInstance(VehicleResponseDto, vehicle)),
    };
  }

  @Get(':id')
  @ApiOperation({ summary: 'Buscar um veículo pelo ID' })
  @ApiResponse({ status: 200, description: 'Veículo encontrado', type: VehicleResponseDto })
  @ApiResponse({ status: 404, description: 'Veículo não encontrado' })
  async findOne(@Param('id') id: string) {
    const vehicle = await this.findVehicleByIdUseCase.execute(id);
    return plainToInstance(VehicleResponseDto, vehicle);
  }

  @Patch(':id')
  @ApiOperation({ summary: 'Atualizar um veículo' })
  @ApiResponse({ status: 200, description: 'Veículo atualizado', type: VehicleResponseDto })
  @ApiResponse({ status: 400, description: 'Dados inválidos' })
  @ApiResponse({ status: 404, description: 'Veículo não encontrado' })
  async update(@Param('id') id: string, @Body() updateVehicleDto: Partial<VehicleCreateDto>) {
    return await this.updateVehicleUseCase.execute(id, updateVehicleDto);
  }

  @Delete(':id')
  @Roles({ roles: ['super-admin'] })
  @ApiOperation({ summary: 'Remover um veículo' })
  @ApiResponse({ status: 204, description: 'Veículo removido' })
  @ApiResponse({ status: 404, description: 'Veículo não encontrado' })
  async remove(@Param('id') id: string) {
    return await this.removeVehicleUseCase.execute(id);
  }
}
