import { ApiProperty } from '@nestjs/swagger';

export class VehicleCreateDto {
  @ApiProperty({ description: 'Renavam do veículo', example: '12345678901' })
  renavam: string;

  @ApiProperty({ description: 'Chassi do veículo', example: '9BWZZZ377VT004251' })
  chassi: string;

  @ApiProperty({ description: 'Marca e modelo do veículo', example: 'Volkswagen Gol', required: false })
  brandModel?: string;

  @ApiProperty({ description: 'Ano do modelo', example: 2020, required: false })
  modelYear?: number;

  @ApiProperty({ description: 'Ano de fabricação', example: 2019, required: false })
  manufactureYear?: number;

  @ApiProperty({ description: 'Cor do veículo', example: 'Preto', required: false })
  color?: string;

  @ApiProperty({ description: 'Tipo de combustível', example: 'Gasolina', required: false })
  fuelType?: string;

  @ApiProperty({ description: 'Placa do veículo', example: 'ABC1234' })
  numberPlate: string;

  @ApiProperty({ description: 'Estado do veículo', example: 'PR' })
  state: string;

  @ApiProperty({ description: 'Cidade do veículo', example: 'Curitiba', required: false })
  city?: string;
}

export class VehicleResponseDto extends VehicleCreateDto {
  @ApiProperty({ description: 'ID do veículo', example: 'uuid-value' })
  id: string;
}
