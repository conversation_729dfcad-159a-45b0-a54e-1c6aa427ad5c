import { Inject, Injectable } from '@nestjs/common';
import { Paginated, PaginateQuery } from 'nestjs-paginate';
import { IVehicleRepository } from '../domain/vehicle.repository.interface';
import { VehicleEntity } from '../infrastructure/vehicle.entity';

@Injectable()
export class FindAllVehiclesUseCase {
  constructor(
    @Inject('IVehicleRepository')
    private readonly vehicleRepository: IVehicleRepository,
  ) {}

  async execute(query: PaginateQuery): Promise<Paginated<VehicleEntity>> {
    return await this.vehicleRepository.findAll(query);
  }
}
