import { BadRequestException, Inject, Injectable } from '@nestjs/common';
import { IVehicleRepository } from '../domain/vehicle.repository.interface';
import { VehicleEntity } from '../infrastructure/vehicle.entity';

@Injectable()
export class FindVehicleByIdUseCase {
  constructor(
    @Inject('IVehicleRepository')
    private readonly vehicleRepository: IVehicleRepository,
  ) {}

  async execute(id: string): Promise<VehicleEntity> {
    const vehicle = await this.vehicleRepository.findOneById(id);

    if (!vehicle) {
      throw new BadRequestException('Veículo não encontrado');
    }

    return vehicle;
  }
}
