import { Inject, Injectable } from '@nestjs/common';
import { IVehicleRepository } from '../domain/vehicle.repository.interface';
import { VehicleEntity } from '../infrastructure/vehicle.entity';

@Injectable()
export class FindVehicleByPlateChassisRenavamUseCase {
  constructor(
    @Inject('IVehicleRepository')
    private readonly vehicleRepository: IVehicleRepository,
  ) {}

  async execute(plate?: string, chassis?: string, renavam?: string): Promise<VehicleEntity | null> {
    return await this.vehicleRepository.findByPlateOrChassisOrRenavam(plate, chassis, renavam);
  }
}
