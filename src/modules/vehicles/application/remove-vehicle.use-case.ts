import { BadRequestException, Inject, Injectable } from '@nestjs/common';
import { IVehicleRepository } from '../domain/vehicle.repository.interface';

@Injectable()
export class RemoveVehicleUseCase {
  constructor(
    @Inject('IVehicleRepository')
    private readonly vehicleRepository: IVehicleRepository,
  ) {}

  async execute(id: string): Promise<void> {
    const vehicle = await this.vehicleRepository.findOneById(id);
    if (!vehicle) {
      throw new BadRequestException('Veículo não encontrado');
    }

    await this.vehicleRepository.delete(id);
  }
}
