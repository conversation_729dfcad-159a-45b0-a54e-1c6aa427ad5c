import { BadRequestException, Inject, Injectable } from '@nestjs/common';
import { IVehicleRepository } from '../domain/vehicle.repository.interface';
import { VehicleCreateDto } from '../presentation/vehicle.dto';

@Injectable()
export class UpdateVehicleUseCase {
  constructor(
    @Inject('IVehicleRepository')
    private readonly vehicleRepository: IVehicleRepository,
  ) {}

  async execute(id: string, updateVehicleDto: Partial<VehicleCreateDto>): Promise<void> {
    const vehicle = await this.vehicleRepository.findOneById(id);

    if (!vehicle) {
      throw new BadRequestException('Veí<PERSON>lo não encontrado');
    }

    await this.vehicleRepository.update(id, updateVehicleDto);
  }
}
