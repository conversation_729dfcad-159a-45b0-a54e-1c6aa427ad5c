import { Inject, Injectable } from '@nestjs/common';
import { IVehicleRepository } from '../domain/vehicle.repository.interface';
import { VehicleEntity } from '../infrastructure/vehicle.entity';
import { VehicleCreateDto } from '../presentation/vehicle.dto';

@Injectable()
export class CreateVehicleUseCase {
  constructor(
    @Inject('IVehicleRepository')
    private readonly vehicleRepository: IVehicleRepository,
  ) {}

  async execute(vehicle: VehicleCreateDto): Promise<VehicleEntity> {
    return this.vehicleRepository.create(vehicle);
  }
}
