import { Inject, Injectable } from '@nestjs/common';
import { VehicleRecall } from '../domain/entities/vehicle-recall.entity';
import { IVehicleRecallsRepository } from '../domain/repositories/vehicle-recalls.repository.interface';

type Input = {
  recall: VehicleRecall;
};

@Injectable()
export class CreateVehicleRecallUseCase {
  constructor(@Inject('IVehicleRecallsRepository') private readonly vehicleRecallsRepository: IVehicleRecallsRepository) {}

  execute({ recall }: Input) {
    return this.vehicleRecallsRepository.create(recall);
  }
}
