import { Provider } from '@nestjs/common';
import { VehicleRecallsRepository } from './infrastructure/repositories/vehicle-recalls.repository';
import { CreateVehicleRecallUseCase } from './application/create-vehicle-recall.use-case';

const REPOSITORIES: Provider[] = [{ provide: 'IVehicleRecallsRepository', useClass: VehicleRecallsRepository }];

export const VEHICLE_RECALLS_PROVIDERS = [...REPOSITORIES, CreateVehicleRecallUseCase];

export const VEHICLE_RECALLS_EXPORTS = ['IVehicleRecallsRepository', CreateVehicleRecallUseCase];
