import { BadRequestException } from '@nestjs/common';

interface VehicleRecallProps {
  id?: string;
  vehicleId: string;
  preSaleImportHistoryVehicleId?: string;
  recall: string;
  description?: string;
  status: string;
  registrationDate: Date;
  deadline: Date;
}

export class VehicleRecall {
  private readonly _id?: string;
  private readonly _vehicleId: string;
  private readonly _preSaleImportHistoryVehicleId?: string;
  private readonly _recall: string;
  private readonly _description?: string;
  private readonly _status: string;
  private readonly _registrationDate: Date;
  private readonly _deadline: Date;

  private constructor(props: VehicleRecallProps) {
    this._id = props.id;
    this._vehicleId = props.vehicleId;
    this._preSaleImportHistoryVehicleId = props.preSaleImportHistoryVehicleId;
    this._recall = props.recall;
    this._description = props.description;
    this._status = props.status;
    this._registrationDate = props.registrationDate;
    this._deadline = props.deadline;
  }

  static create(props: VehicleRecallProps): VehicleRecall {
    this.validateVehicleId(props.vehicleId);
    this.validateRecall(props.recall);
    this.validateStatus(props.status);
    this.validateRegistrationDate(props.registrationDate);
    this.validateDeadline(props.deadline);
    return new VehicleRecall(props);
  }

  private static validateVehicleId(vehicleId: string) {
    if (!vehicleId) {
      throw new BadRequestException('ID do veículo é obrigatório');
    }
  }

  private static validateRecall(recall: string) {
    if (!recall) {
      throw new BadRequestException('O recall é obrigatório');
    }
  }

  private static validateStatus(status: string) {
    if (!status) {
      throw new BadRequestException('O status é obrigatório');
    }
  }

  private static validateRegistrationDate(registrationDate: Date): void {
    if (!registrationDate) {
      throw new BadRequestException('A data de registro do recall é obrigatória');
    }

    if (registrationDate < new Date(1900, 0, 1)) {
      throw new BadRequestException('Data de registro do recall inválida');
    }
  }

  private static validateDeadline(deadline: Date): void {
    if (!deadline) {
      throw new BadRequestException('A data limite do recall é obrigatória');
    }

    if (deadline < new Date(1900, 0, 1)) {
      throw new BadRequestException('Data limite do recall inválida');
    }
  }

  get id(): string {
    return this._id;
  }

  get vehicleId(): string {
    return this._vehicleId;
  }

  get preSaleImportHistoryVehicleId(): string {
    return this._preSaleImportHistoryVehicleId;
  }

  get recall(): string {
    return this._recall;
  }

  get description(): string {
    return this._description;
  }

  get status(): string {
    return this._status;
  }

  get registrationDate(): Date {
    return this._registrationDate;
  }

  get deadline(): Date {
    return this._deadline;
  }
}
