import { <PERSON>um<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne } from 'typeorm';
import { BaseEntity } from '@/core/infrastructure/common/abstracts/entity.abstract';
import { VehicleEntity } from '@/modules/vehicles/infrastructure/vehicle.entity';
import { PreSaleImportHistoryVehicleEntity } from '@/modules/pre-sales/infrastructure/entities/pre-sale-import-history-vehicle.entity';

@Entity('vehicle_recalls')
export class VehicleRecallEntity extends BaseEntity {
  @Column({ name: 'vehicle_id' })
  vehicleId: string;

  @ManyToOne(() => VehicleEntity)
  @JoinColumn({ name: 'vehicle_id' })
  vehicle: VehicleEntity;

  @Column({ name: 'pre_sale_import_history_vehicle_id', nullable: true })
  preSaleImportHistoryVehicleId?: string;

  @ManyToOne(() => PreSaleImportHistoryVehicleEntity, { nullable: true })
  @JoinColumn({ name: 'pre_sale_import_history_vehicle_id' })
  preSaleImportHistoryVehicle?: PreSaleImportHistoryVehicleEntity;

  @Column()
  recall: string;

  @Column({ nullable: true })
  description?: string;

  @Column()
  status: string;

  @Column({ name: 'registration_date', type: 'date' })
  registrationDate: Date;

  @Column({ type: 'date' })
  deadline: Date;
}
