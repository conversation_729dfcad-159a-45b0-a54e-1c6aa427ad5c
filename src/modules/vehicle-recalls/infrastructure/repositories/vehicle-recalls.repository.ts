import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { VehicleRecall } from '../../domain/entities/vehicle-recall.entity';
import { IVehicleRecallsRepository } from '../../domain/repositories/vehicle-recalls.repository.interface';
import { VehicleRecallEntity } from '../entities/vehicle-recall.entity';

@Injectable()
export class VehicleRecallsRepository implements IVehicleRecallsRepository {
  constructor(@InjectRepository(VehicleRecallEntity) private readonly repository: Repository<VehicleRecallEntity>) {}

  private toDomain(entity: VehicleRecallEntity): VehicleRecall {
    return VehicleRecall.create({
      id: entity.id,
      vehicleId: entity.vehicleId,
      preSaleImportHistoryVehicleId: entity.preSaleImportHistoryVehicleId,
      recall: entity.recall,
      description: entity.description,
      status: entity.status,
      registrationDate: entity.registrationDate,
      deadline: entity.deadline,
    });
  }

  private toEntity(domain: VehicleRecall): Partial<VehicleRecallEntity> {
    return {
      id: domain.id,
      vehicleId: domain.vehicleId,
      preSaleImportHistoryVehicleId: domain.preSaleImportHistoryVehicleId,
      recall: domain.recall,
      description: domain.description,
      status: domain.status,
      registrationDate: domain.registrationDate,
      deadline: domain.deadline,
    };
  }

  async create(recall: VehicleRecall): Promise<VehicleRecall> {
    const entity = this.repository.create(this.toEntity(recall));
    const saved = await this.repository.save(entity);
    return this.toDomain(saved);
  }

  async getByVehicleId(vehicleId: string): Promise<VehicleRecall[]> {
    const results = await this.repository.findBy({ vehicleId });
    return results.map((entity) => this.toDomain(entity));
  }

  async getByPreSaleVehicleId(preSaleVehicleId: string): Promise<VehicleRecall[]> {
    const results = await this.repository.findBy({ preSaleImportHistoryVehicleId: preSaleVehicleId });
    return results.map((entity) => this.toDomain(entity));
  }
}
