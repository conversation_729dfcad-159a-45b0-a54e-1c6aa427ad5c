import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { VehicleRecallEntity } from './infrastructure/entities/vehicle-recall.entity';
import { VEHICLE_RECALLS_EXPORTS, VEHICLE_RECALLS_PROVIDERS } from './vehicle-recalls.providers';

@Module({
  imports: [TypeOrmModule.forFeature([VehicleRecallEntity])],
  providers: VEHICLE_RECALLS_PROVIDERS,
  exports: VEHICLE_RECALLS_EXPORTS,
})
export class VehicleRecallsModule {}
