import { App<PERSON>ontroller } from '@/app.controller';
import { AppConfig, AuthConfig, DatabaseConfig } from '@/config';
import { ClientsModule } from '@/modules/clients/clients.module';
import { PreSalesModule } from '@/modules/pre-sales/pre-sales.module';
import { VehiclesModule } from '@/modules/vehicles/vehicles.module';
import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { ScheduleModule } from '@nestjs/schedule';
import { TypeOrmModule } from '@nestjs/typeorm';
import { KeycloakConnectConfig, KeycloakConnectModule } from 'nest-keycloak-connect';
import { PROVIDERS } from './app.providers';
import { DashboardModule } from './modules/dashboard/dashboard.module';
import { VehicleDebitsModule } from './modules/vehicle-debits/vehicle-debits.module';
import { VehicleFinesModule } from './modules/vehicle-fines/vehicle-fines.module';
import { VehicleRecallsModule } from './modules/vehicle-recalls/vehicle-recalls.module';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      cache: true,
      load: [AppConfig, DatabaseConfig, AuthConfig],
    }),
    KeycloakConnectModule.registerAsync({
      inject: [ConfigService],
      useFactory: (configService: ConfigService) => ({
        ...configService.get<KeycloakConnectConfig>('auth'),
      }),
    }),
    TypeOrmModule.forRootAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: (configService: ConfigService) => ({
        ...configService.get<any>('database'),
      }),
    }),
    ScheduleModule.forRoot(),
    VehiclesModule,
    VehicleDebitsModule,
    ClientsModule,
    DashboardModule,
    PreSalesModule,
    VehicleFinesModule,
    VehicleRecallsModule,
  ],
  controllers: [AppController],
  providers: PROVIDERS,
})
export class AppModule {}
