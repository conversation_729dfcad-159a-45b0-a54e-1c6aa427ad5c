// Refs.: https://medium.com/nexton/how-to-establish-a-secure-connection-from-a-node-js-api-to-an-aws-rds-f79c5daa2ea5
// Refs.: https://docs.aws.amazon.com/AmazonRDS/latest/UserGuide/UsingWithRDS.SSL.html
import * as fs from 'fs';

import { ConfigService } from '@nestjs/config';
import { config } from 'dotenv';
import { DataSource } from 'typeorm';

config();

const configService = new ConfigService();

const host = configService.get('DB_HOST') === 'db' ? 'localhost' : configService.get('DB_HOST');

export default new DataSource({
  type: 'postgres',
  host: host,
  port: +configService.get('DB_PORT'),
  username: configService.get('DB_USER'),
  password: configService.get('DB_PASS'),
  database: configService.get('DB_NAME'),
  entities: [`${__dirname}/../src/**/*.entity{.ts,.js}`],
  logging: configService.get('nodenv') === 'development',
  migrations: [`${__dirname}/migrations/*{.ts,.js}`],
  ssl:
    process.env.NODE_ENV != 'production'
      ? false
      : {
          ca: fs.readFileSync(`${__dirname}/db/certs/aws-rds-us-east-1-bundle.pem`).toString(),
        },
});
