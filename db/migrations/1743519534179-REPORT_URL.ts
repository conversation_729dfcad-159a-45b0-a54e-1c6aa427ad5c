import { MigrationInterface, QueryRunner } from "typeorm";

export class REPORTURL1743519534179 implements MigrationInterface {
    name = 'REPORTURL1743519534179'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "pre_sale_import_history" ADD "report_url" character varying`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "pre_sale_import_history" DROP COLUMN "report_url"`);
    }

}
