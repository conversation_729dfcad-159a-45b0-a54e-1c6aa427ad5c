import { MigrationInterface, QueryRunner } from "typeorm";

export class INITIAL1742755880126 implements MigrationInterface {
    name = 'INITIAL1742755880126'

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`CREATE TABLE "vehicles" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, "renavam" character varying(11) NOT NULL, "chassi" character varying(17) NOT NULL, "brand_model" character varying, "model_year" integer, "manufacture_year" integer, "color" character varying, "fuel_type" character varying, "number_plate" character varying(7) NOT NULL, "state" character varying(2) NOT NULL DEFAULT 'PR', "city" character varying, CONSTRAINT "UQ_ed55567385be4e72ad03de2f8c5" UNIQUE ("number_plate"), CONSTRAINT "UQ_048ff68a50cc7e29d16dde333cd" UNIQUE ("chassi"), CONSTRAINT "UQ_f20513b1dd64f0b2da6f91ef540" UNIQUE ("renavam"), CONSTRAINT "PK_18d8646b59304dce4af3a9e35b6" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE INDEX "IDX_f20513b1dd64f0b2da6f91ef54" ON "vehicles" ("renavam") `);
        await queryRunner.query(`CREATE INDEX "IDX_048ff68a50cc7e29d16dde333c" ON "vehicles" ("chassi") `);
        await queryRunner.query(`CREATE INDEX "IDX_ed55567385be4e72ad03de2f8c" ON "vehicles" ("number_plate") `);
        await queryRunner.query(`CREATE TYPE "public"."pre_sale_import_history_status_enum" AS ENUM('STARTED', 'PROCESSING', 'COMPLETED', 'ERROR')`);
        await queryRunner.query(`CREATE TABLE "pre_sale_import_history" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, "file_name" character varying NOT NULL, "original_file_path" character varying NOT NULL, "status" "public"."pre_sale_import_history_status_enum" NOT NULL DEFAULT 'STARTED', "total_records" integer NOT NULL DEFAULT '0', "processed_records" integer NOT NULL DEFAULT '0', "success_count" integer NOT NULL DEFAULT '0', "error_count" integer NOT NULL DEFAULT '0', "already_registered_count" integer NOT NULL DEFAULT '0', CONSTRAINT "PK_4995aea5cb97a767b22f800fc61" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE INDEX "IDX_f7b3bf2913e61442f4d48eab46" ON "pre_sale_import_history" ("file_name") `);
        await queryRunner.query(`CREATE INDEX "IDX_32f782f049e11529ace503762b" ON "pre_sale_import_history" ("status") `);
        await queryRunner.query(`CREATE TYPE "public"."pre_sale_import_history_vehicles_status_enum" AS ENUM('STARTED', 'PROCESSING', 'SUCCESS', 'ERROR')`);
        await queryRunner.query(`CREATE TABLE "pre_sale_import_history_vehicles" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, "import_history_id" uuid NOT NULL, "vehicle_id" uuid, "line_number" integer NOT NULL, "status" "public"."pre_sale_import_history_vehicles_status_enum" NOT NULL, "error_message" character varying, CONSTRAINT "PK_3ad5a738f9bf65da5910beb2691" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE INDEX "IDX_a224e90c28d8d8a35095199642" ON "pre_sale_import_history_vehicles" ("import_history_id") `);
        await queryRunner.query(`CREATE INDEX "IDX_a70db7ef9527997437b2de5715" ON "pre_sale_import_history_vehicles" ("vehicle_id") `);
        await queryRunner.query(`CREATE TABLE "clients" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, "name" character varying NOT NULL, "description" character varying, "active" boolean NOT NULL DEFAULT true, CONSTRAINT "PK_f1ab7cf3a5714dbc6bb4e1c28a4" PRIMARY KEY ("id"))`);
        await queryRunner.query(`CREATE TABLE "vehicle_debits" ("id" uuid NOT NULL DEFAULT uuid_generate_v4(), "created_at" TIMESTAMP NOT NULL DEFAULT now(), "updated_at" TIMESTAMP NOT NULL DEFAULT now(), "deleted_at" TIMESTAMP, "vehicle_id" uuid NOT NULL, "description" character varying(255) NOT NULL, "value" numeric(10,2) NOT NULL, "due_date" TIMESTAMP NOT NULL, "status" character varying(20) NOT NULL, "type" character varying(20) NOT NULL, CONSTRAINT "PK_2c7c4b9e224a891067a4a0d4da8" PRIMARY KEY ("id"))`);
        await queryRunner.query(`ALTER TABLE "pre_sale_import_history_vehicles" ADD CONSTRAINT "FK_a224e90c28d8d8a35095199642e" FOREIGN KEY ("import_history_id") REFERENCES "pre_sale_import_history"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "pre_sale_import_history_vehicles" ADD CONSTRAINT "FK_a70db7ef9527997437b2de57157" FOREIGN KEY ("vehicle_id") REFERENCES "vehicles"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
        await queryRunner.query(`ALTER TABLE "vehicle_debits" ADD CONSTRAINT "FK_95b8edc8eea7b4b8c79ebd11f3c" FOREIGN KEY ("vehicle_id") REFERENCES "vehicles"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`ALTER TABLE "vehicle_debits" DROP CONSTRAINT "FK_95b8edc8eea7b4b8c79ebd11f3c"`);
        await queryRunner.query(`ALTER TABLE "pre_sale_import_history_vehicles" DROP CONSTRAINT "FK_a70db7ef9527997437b2de57157"`);
        await queryRunner.query(`ALTER TABLE "pre_sale_import_history_vehicles" DROP CONSTRAINT "FK_a224e90c28d8d8a35095199642e"`);
        await queryRunner.query(`DROP TABLE "vehicle_debits"`);
        await queryRunner.query(`DROP TABLE "clients"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_a70db7ef9527997437b2de5715"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_a224e90c28d8d8a35095199642"`);
        await queryRunner.query(`DROP TABLE "pre_sale_import_history_vehicles"`);
        await queryRunner.query(`DROP TYPE "public"."pre_sale_import_history_vehicles_status_enum"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_32f782f049e11529ace503762b"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_f7b3bf2913e61442f4d48eab46"`);
        await queryRunner.query(`DROP TABLE "pre_sale_import_history"`);
        await queryRunner.query(`DROP TYPE "public"."pre_sale_import_history_status_enum"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_ed55567385be4e72ad03de2f8c"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_048ff68a50cc7e29d16dde333c"`);
        await queryRunner.query(`DROP INDEX "public"."IDX_f20513b1dd64f0b2da6f91ef54"`);
        await queryRunner.query(`DROP TABLE "vehicles"`);
    }

}
