import { MigrationInterface, QueryRunner } from 'typeorm';

export class PRESALESRESULTIMPORT1750514549793 implements MigrationInterface {
  name = 'PRESALESRESULTIMPORT1750514549793';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`CREATE TABLE "vehicle_fines"
                             (
                               "id"                                 uuid                   NOT NULL DEFAULT uuid_generate_v4(),
                               "created_at"                         TIMESTAMP              NOT NULL DEFAULT now(),
                               "updated_at"                         TIMESTAMP              NOT NULL DEFAULT now(),
                               "deleted_at"                         TIMESTAMP,
                               "vehicle_id"                         uuid                   NOT NULL,
                               "pre_sale_import_history_vehicle_id" uuid,
                               "fine_code"                          character varying      NOT NULL,
                               "status"                             character varying(20)  NOT NULL,
                               "date"                               date                   NOT NULL,
                               "time"                               TIME                   NOT NULL,
                               "enforcing_authority"                character varying(100) NOT NULL,
                               "infraction_code"                    character varying(10)  NOT NULL,
                               "description"                        character varying(255) NOT NULL,
                               "location"                           character varying(255) NOT NULL,
                               "value"                              numeric(10, 2)         NOT NULL,
                               "due_date"                           TIMESTAMP,
                               "bar_code"                           character varying(48),
                               CONSTRAINT "PK_b6725cf4f570b19cbd60a595357" PRIMARY KEY ("id")
                             )`);
    await queryRunner.query(`CREATE TABLE "vehicle_recalls"
                             (
                               "id"                                 uuid              NOT NULL DEFAULT uuid_generate_v4(),
                               "created_at"                         TIMESTAMP         NOT NULL DEFAULT now(),
                               "updated_at"                         TIMESTAMP         NOT NULL DEFAULT now(),
                               "deleted_at"                         TIMESTAMP,
                               "vehicle_id"                         uuid              NOT NULL,
                               "pre_sale_import_history_vehicle_id" uuid,
                               "recall"                             character varying NOT NULL,
                               "description"                        character varying,
                               "status"                             character varying NOT NULL,
                               "registration_date"                  date              NOT NULL,
                               "deadline"                           date              NOT NULL,
                               CONSTRAINT "PK_646096786ba7bc285c94037387c" PRIMARY KEY ("id")
                             )`);
    await queryRunner.query(`ALTER TABLE "vehicle_debits"
      ADD "pre_sale_import_history_vehicle_id" uuid`);
    await queryRunner.query(`ALTER TABLE "vehicles"
      ADD "category" character varying`);
    await queryRunner.query(`ALTER TABLE "vehicles"
      ADD "situation" character varying`);
    await queryRunner.query(`ALTER TABLE "vehicles"
      ADD "gravame_status" character varying`);
    await queryRunner.query(`ALTER TABLE "vehicles"
      ADD "gravame_situation" character varying`);
    await queryRunner.query(`ALTER TABLE "vehicles"
      ADD "owner" character varying`);
    await queryRunner.query(`ALTER TABLE "vehicles"
      ADD "owner_document" character varying`);
    await queryRunner.query(`ALTER TABLE "vehicles"
      ADD "last_csv_report" TIMESTAMP`);
    await queryRunner.query(`ALTER TABLE "vehicles"
      ADD "regularization_deadline" TIMESTAMP`);
    await queryRunner.query(`ALTER TABLE "vehicles"
      ADD "blocking_reason" character varying`);
    await queryRunner.query(`ALTER TABLE "vehicles"
      ADD "last_licensing" character varying`);
    await queryRunner.query(`ALTER TABLE "vehicle_debits"
      ADD CONSTRAINT "FK_0b4a05f8426aecb6475f836a9bc" FOREIGN KEY ("pre_sale_import_history_vehicle_id") REFERENCES "pre_sale_import_history_vehicles" ("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    await queryRunner.query(`ALTER TABLE "vehicle_debits" ALTER COLUMN "due_date" DROP NOT NULL`);
    await queryRunner.query(`ALTER TABLE "vehicle_fines"
      ADD CONSTRAINT "FK_7df5161c84b5fb7cc228cfd53ef" FOREIGN KEY ("vehicle_id") REFERENCES "vehicles" ("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    await queryRunner.query(`ALTER TABLE "vehicle_fines"
      ADD CONSTRAINT "FK_77b957c37b45a47441a76f4cfb4" FOREIGN KEY ("pre_sale_import_history_vehicle_id") REFERENCES "pre_sale_import_history_vehicles" ("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    await queryRunner.query(`ALTER TABLE "vehicle_recalls"
      ADD CONSTRAINT "FK_92c5ca26973ed790f4afc7b4d04" FOREIGN KEY ("vehicle_id") REFERENCES "vehicles" ("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
    await queryRunner.query(`ALTER TABLE "vehicle_recalls"
      ADD CONSTRAINT "FK_0a34c6ddf95a7a5cdd9c2a2351d" FOREIGN KEY ("pre_sale_import_history_vehicle_id") REFERENCES "pre_sale_import_history_vehicles" ("id") ON DELETE NO ACTION ON UPDATE NO ACTION`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "vehicle_recalls" DROP CONSTRAINT "FK_0a34c6ddf95a7a5cdd9c2a2351d"`);
    await queryRunner.query(`ALTER TABLE "vehicle_recalls" DROP CONSTRAINT "FK_92c5ca26973ed790f4afc7b4d04"`);
    await queryRunner.query(`ALTER TABLE "vehicle_fines" DROP CONSTRAINT "FK_77b957c37b45a47441a76f4cfb4"`);
    await queryRunner.query(`ALTER TABLE "vehicle_fines" DROP CONSTRAINT "FK_7df5161c84b5fb7cc228cfd53ef"`);
    await queryRunner.query(`ALTER TABLE "vehicle_debits" DROP CONSTRAINT "FK_0b4a05f8426aecb6475f836a9bc"`);
    await queryRunner.query(`ALTER TABLE "vehicle_debits" ALTER COLUMN "due_date" SET NOT NULL`);
    await queryRunner.query(`ALTER TABLE "vehicles" DROP COLUMN "last_licensing"`);
    await queryRunner.query(`ALTER TABLE "vehicles" DROP COLUMN "blocking_reason"`);
    await queryRunner.query(`ALTER TABLE "vehicles" DROP COLUMN "regularization_deadline"`);
    await queryRunner.query(`ALTER TABLE "vehicles" DROP COLUMN "last_csv_report"`);
    await queryRunner.query(`ALTER TABLE "vehicles" DROP COLUMN "owner_document"`);
    await queryRunner.query(`ALTER TABLE "vehicles" DROP COLUMN "owner"`);
    await queryRunner.query(`ALTER TABLE "vehicles" DROP COLUMN "gravame_situation"`);
    await queryRunner.query(`ALTER TABLE "vehicles" DROP COLUMN "gravame_status"`);
    await queryRunner.query(`ALTER TABLE "vehicles" DROP COLUMN "situation"`);
    await queryRunner.query(`ALTER TABLE "vehicles" DROP COLUMN "category"`);
    await queryRunner.query(`ALTER TABLE "vehicle_debits" DROP COLUMN "pre_sale_import_history_vehicle_id"`);
    await queryRunner.query(`DROP TABLE "vehicle_recalls"`);
    await queryRunner.query(`DROP TABLE "vehicle_fines"`);
  }
}
