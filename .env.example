# API
NODE_ENV=development
UPLOADED_FILES_DESTINATION=files
TEMP_FILE_DESTINATION=temp
PARSER_API_URL=http://develop-platform-parser-api.us-east-1.elasticbeanstalk.com/
MIGRATIONS_RUN=true
DB_USER=postgres
DB_PORT=5432
DB_HOST=localhost
DB_PASS=postgres
DB_NAME=develop_dev_db

#Auth
AUTH_URL=http://develop-platform-iam.us-east-1.elasticbeanstalk.com/
AUTH_REALM=dev
AUTH_CLIENT_ID=develop-platform-api
AUTH_CLIENT_SECRET=VVwF1DbRUswMCNxXGqI1jK1qac6bj5KL

# Celery
RABBITMQ_SERVER_URI=amqp://admin:admin@localhost:5672
PARSER_TASKS_QUEUE=parser-tasks-dev
PARSER_TASKS_RESULTS=parser-tasks-results

# POSTGRES CONTAINER
POSTGRES_MULTIPLE_DATABASES=develop_db
POSTGRES_PASSWORD=postgres
