#!/bin/bash

# API
#NODE_ENV=production
NODE_ENV=development

UPLOADED_FILES_DESTINATION=files
TEMP_FILE_DESTINATION=temp
#PARSER_API_URL=http://develop-platform-parser-api.us-east-1.elasticbeanstalk.com/
PARSER_API_URL=http://localhost:8000/
MIGRATIONS_RUN=true
DB_USER=postgres
DB_PORT=5432
DB_HOST=localhost
DB_PASS=postgres
DB_NAME=develop_dev_db

#B_HOST=develop-db.cnqkyhkwvwld.us-east-1.rds.amazonaws.com
#B_PASS=avQD1!5ug22R
#B_NAME=develop_db

#Auth
#AUTH_URL=http://develop-platform-iam.us-east-1.elasticbeanstalk.com
AUTH_URL=http://localhost:8080
AUTH_REALM=master
AUTH_CLIENT_ID=develop-platform-api
AUTH_CLIENT_SECRET=QCznM9ckIvMy56MUjVfI5vRhcnTKzfKm

# Celery
RABBITMQ_SERVER_URI=amqp://admin:admin@localhost:5672
PARSER_TASKS_QUEUE=parser-tasks
PARSER_TASKS_RESULTS=parser-tasks-results

# POSTGRES CONTAINER
POSTGRES_MULTIPLE_DATABASES=develop_dev_db
POSTGRES_PASSWORD=postgres
npm run start:dev
# clinic heapprofiler -- node dist/src/main
