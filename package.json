{"name": "develop-platform-api", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"prebuild": "<PERSON><PERSON><PERSON> dist", "build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "typeorm": "ts-node --require tsconfig-paths/register ./node_modules/typeorm/cli", "migration:run": "npm run typeorm migration:run -- -d ./db/typeorm.config.ts", "migration:generate": "npm run typeorm -- -d ./db/typeorm.config.ts migration:generate ./db/migrations/$npm_config_name", "migration:create": "npm run typeorm -- migration:create ./db/migrations/$npm_config_name", "migration:revert": "npm run typeorm -- -d ./db/typeorm.config.ts migration:revert", "migration:show": "npm run typeorm -- migration:show -d ./db/typeorm.config.ts"}, "dependencies": {"@nestjs/axios": "^3.1.3", "@nestjs/common": "^10.4.15", "@nestjs/config": "^3.3.0", "@nestjs/core": "^10.4.15", "@nestjs/cqrs": "^11.0.2", "@nestjs/event-emitter": "^2.1.1", "@nestjs/jwt": "^10.2.0", "@nestjs/platform-express": "^10.4.15", "@nestjs/schedule": "^3.0.4", "@nestjs/swagger": "^8.1.1", "@nestjs/typeorm": "^10.0.2", "@types/xlsx": "^0.0.35", "amqplib": "^0.10.3", "celery-node": "^0.5.9", "class-validator": "^0.14.0", "compression": "^1.8.0", "cross-env": "^7.0.3", "csv": "^6.2.6", "date-fns": "^4.1.0", "dotenv": "^16.0.3", "exceljs": "^4.3.0", "helmet": "^8.0.0", "keycloak-connect": "^26.1.1", "module-alias": "^2.2.3", "nest-csv-parser": "^2.0.4", "nest-keycloak-connect": "^1.10.1", "nestjs-paginate": "^11.1.1", "papaparse": "^5.3.2", "pg": "^8.9.0", "pg-hstore": "^2.3.4", "postgresql": "^0.0.1", "read-excel-file": "^5.8.6", "reflect-metadata": "^0.1.13", "rimraf": "^4.1.2", "rxjs": "^7.8.0", "sequelize": "^6.28.0", "sequelize-typescript": "^2.1.5", "swagger-ui-express": "^4.6.0", "typeorm": "^0.3.17", "uuid": "^11.1.0", "xlsx": "^0.18.5"}, "devDependencies": {"@nestjs/cli": "^10.1.18", "@nestjs/schematics": "^10.0.2", "@nestjs/testing": "^10.2.7", "@types/amqplib": "^0.10.1", "@types/express": "^4.17.17", "@types/jest": "29.4.0", "@types/module-alias": "^2.0.4", "@types/multer": "^1.4.8", "@types/node": "^18.11.18", "@types/papaparse": "^5.3.7", "@types/sequelize": "^4.28.14", "@types/supertest": "^2.0.12", "@types/uuid": "^9.0.2", "@typescript-eslint/eslint-plugin": "^5.50.0", "@typescript-eslint/parser": "^5.50.0", "eslint": "^8.33.0", "eslint-config-prettier": "^8.6.0", "eslint-plugin-prettier": "^4.2.1", "jest": "^29.4.1", "prettier": "^2.8.3", "source-map-support": "^0.5.21", "supertest": "^6.3.3", "ts-jest": "^29.0.5", "ts-loader": "^9.4.2", "ts-node": "^10.9.1", "tsconfig-paths": "^4.1.2", "typescript": "^4.9.5"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}, "_moduleAliases": {"@": "dist"}}